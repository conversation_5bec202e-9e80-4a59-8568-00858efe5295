#!/usr/bin/env python3
"""
強制啟動 Reddit 監控系統
跳過 API 驗證直接開始運行
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.schedulers.task_scheduler import TaskScheduler
from src.utils.config import Config
from src.storage.database import DatabaseManager
import logging
from datetime import datetime
import signal

class ForceStartMonitor:
    """強制啟動監控系統"""
    
    def __init__(self):
        self.config = Config()
        self.logger = self._setup_logger()
        self.scheduler = None
        self.running = False
        
        # 設置信號處理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger('ForceStartMonitor')
        return logger
    
    def _signal_handler(self, signum, frame):
        """信號處理器"""
        self.logger.info(f"收到信號 {signum}，正在停止系統...")
        self.stop()
    
    def setup_database(self):
        """設置數據庫"""
        try:
            db = DatabaseManager()
            db.setup_default_subreddits()
            self.logger.info("✅ 數據庫設置完成")
            return True
        except Exception as e:
            self.logger.error(f"❌ 數據庫設置失敗: {e}")
            return False
    
    def start_monitoring(self):
        """強制啟動監控系統"""
        self.logger.info("🚀 強制啟動 Reddit 股票加密貨幣監控系統...")
        
        try:
            # 設置數據庫
            if not self.setup_database():
                return
            
            # 顯示系統信息
            self.logger.info("=" * 60)
            self.logger.info("Reddit 股票加密貨幣情緒監控系統 - 強制啟動模式")
            self.logger.info("=" * 60)
            self.logger.info(f"啟動時間: {datetime.now()}")
            self.logger.info(f"數據庫: {self.config.DATABASE_PATH}")
            self.logger.info(f"報告目錄: {self.config.REPORTS_DIR}")
            self.logger.info("⚠️  注意: 跳過 API 驗證，如果 API 憑證不正確將在運行時報錯")
            self.logger.info("=" * 60)
            
            # 創建並啟動調度器
            self.scheduler = TaskScheduler()
            self.running = True
            
            # 啟動調度器
            self.scheduler.start()
            
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號")
        except Exception as e:
            self.logger.error(f"❌ 系統啟動失敗: {e}")
            raise
        finally:
            self.stop()
    
    def stop(self):
        """停止系統"""
        if self.running:
            self.logger.info("⏹️ 停止系統...")
            self.running = False
            
            if self.scheduler:
                self.scheduler.stop()
            
            self.logger.info("✅ 系統已停止")

def main():
    """主函數"""
    print("🚀 Reddit 股票加密貨幣監控系統 - 強制啟動模式")
    print("⚠️  此模式將跳過 API 驗證直接啟動系統")
    print("📊 如果 API 憑證不正確，系統將在運行時報錯")
    print("⏹️  按 Ctrl+C 停止監控")
    print("=" * 60)
    
    monitor = ForceStartMonitor()
    monitor.start_monitoring()

if __name__ == "__main__":
    main()