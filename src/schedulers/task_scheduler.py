"""
任務調度系統
使用 APScheduler 管理定時任務
"""

import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
import time

from ..collectors.reddit_collector import RedditCollector
from ..analyzers.ollama_sentiment_analyzer import OllamaSentimentAnalyzer
from ..analyzers.entity_extractor import EntityExtractor
from ..analyzers.trend_analyzer import TrendAnalyzer
from ..storage.database import DatabaseManager
from ..reporters.report_generator import ReportGenerator
from ..utils.config import Config


class TaskScheduler:
    """任務調度器"""
    
    def __init__(self):
        self.config = Config()
        self.scheduler = BlockingScheduler()
        self.logger = self._setup_logger()
        
        # 初始化組件
        self.reddit_collector = RedditCollector()
        self.sentiment_analyzer = OllamaSentimentAnalyzer()
        self.entity_extractor = EntityExtractor()
        self.trend_analyzer = TrendAnalyzer()
        self.report_generator = ReportGenerator()
        self.db = DatabaseManager()
        
        # 設置事件監聽器
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        
        # 添加任務
        self._setup_jobs()
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('TaskScheduler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_jobs(self):
        """設置定時任務"""
        
        # 1. 數據收集任務 - 每小時執行
        self.scheduler.add_job(
            func=self.collect_data_job,
            trigger=IntervalTrigger(hours=1),
            id='collect_data',
            name='收集 Reddit 數據',
            max_instances=1,
            replace_existing=True
        )
        
        # 2. 情緒分析任務 - 每小時執行（在數據收集後）
        self.scheduler.add_job(
            func=self.analyze_sentiment_job,
            trigger=IntervalTrigger(hours=1, start_date=datetime.now() + timedelta(minutes=10)),
            id='analyze_sentiment',
            name='分析情緒',
            max_instances=1,
            replace_existing=True
        )
        
        # 3. 實體提取任務 - 每小時執行（在數據收集後）
        self.scheduler.add_job(
            func=self.extract_entities_job,
            trigger=IntervalTrigger(hours=1, start_date=datetime.now() + timedelta(minutes=15)),
            id='extract_entities',
            name='提取實體',
            max_instances=1,
            replace_existing=True
        )
        
        # 4. 趨勢計算任務 - 每小時執行（在分析後）
        self.scheduler.add_job(
            func=self.calculate_trends_job,
            trigger=IntervalTrigger(hours=1, start_date=datetime.now() + timedelta(minutes=25)),
            id='calculate_trends',
            name='計算趨勢',
            max_instances=1,
            replace_existing=True
        )
        
        # 5. 報告生成任務 - 每小時執行（在所有分析完成後）
        self.scheduler.add_job(
            func=self.generate_report_job,
            trigger=IntervalTrigger(hours=1, start_date=datetime.now() + timedelta(minutes=35)),
            id='generate_report',
            name='生成報告',
            max_instances=1,
            replace_existing=True
        )
        
        # 6. 數據清理任務 - 每天凌晨 2 點執行
        self.scheduler.add_job(
            func=self.cleanup_data_job,
            trigger=CronTrigger(hour=2, minute=0),
            id='cleanup_data',
            name='清理舊數據',
            max_instances=1,
            replace_existing=True
        )
        
        # 7. 更新 subreddit 統計 - 每天執行一次
        self.scheduler.add_job(
            func=self.update_subreddit_stats_job,
            trigger=CronTrigger(hour=6, minute=0),
            id='update_subreddit_stats',
            name='更新 Subreddit 統計',
            max_instances=1,
            replace_existing=True
        )
        
        # 8. 快速測試任務 - 每 5 分鐘執行（用於測試）
        self.scheduler.add_job(
            func=self.quick_test_job,
            trigger=IntervalTrigger(minutes=5),
            id='quick_test',
            name='快速測試',
            max_instances=1,
            replace_existing=True
        )
    
    def collect_data_job(self):
        """數據收集任務"""
        try:
            self.logger.info("🔄 開始收集 Reddit 數據...")
            
            # 收集股票相關數據
            stock_results = self.reddit_collector.collect_all_subreddits("stock")
            self.logger.info(f"股票數據收集完成: {stock_results}")
            
            # 收集加密貨幣相關數據
            crypto_results = self.reddit_collector.collect_all_subreddits("crypto")
            self.logger.info(f"加密貨幣數據收集完成: {crypto_results}")
            
            total_posts = stock_results['posts'] + crypto_results['posts']
            total_comments = stock_results['comments'] + crypto_results['comments']
            
            self.logger.info(f"✅ 數據收集完成: {total_posts} 個帖子, {total_comments} 個評論")
            
        except Exception as e:
            self.logger.error(f"❌ 數據收集失敗: {e}")
    
    def analyze_sentiment_job(self):
        """情緒分析任務"""
        try:
            self.logger.info("🎭 開始情緒分析...")
            
            # 分析最近的帖子
            posts_analyzed = self.sentiment_analyzer.analyze_recent_posts(hours=2)
            
            # 分析最近的評論
            comments_analyzed = self.sentiment_analyzer.analyze_recent_comments(hours=2)
            
            self.logger.info(f"✅ 情緒分析完成: {posts_analyzed} 個帖子, {comments_analyzed} 個評論")
            
        except Exception as e:
            self.logger.error(f"❌ 情緒分析失敗: {e}")
    
    def extract_entities_job(self):
        """實體提取任務"""
        try:
            self.logger.info("🔍 開始實體提取...")
            
            # 從最近的帖子提取實體
            posts_entities = self.entity_extractor.extract_from_recent_posts(hours=2)
            
            # 從最近的評論提取實體
            comments_entities = self.entity_extractor.extract_from_recent_comments(hours=2)
            
            self.logger.info(f"✅ 實體提取完成: 帖子 {posts_entities} 個實體, 評論 {comments_entities} 個實體")
            
        except Exception as e:
            self.logger.error(f"❌ 實體提取失敗: {e}")
    
    def calculate_trends_job(self):
        """趨勢計算任務"""
        try:
            self.logger.info("📈 開始計算趨勢...")
            
            # 計算所有時間段的趨勢
            trends = self.trend_analyzer.calculate_all_trends()
            
            self.logger.info(f"✅ 趨勢計算完成: {trends}")
            
        except Exception as e:
            self.logger.error(f"❌ 趨勢計算失敗: {e}")
    
    def generate_report_job(self):
        """報告生成任務"""
        try:
            self.logger.info("📊 開始生成報告...")

            # 生成每小時報告
            report_path = self.report_generator.generate_hourly_report()

            self.logger.info(f"✅ 報告生成完成: {report_path}")

        except Exception as e:
            self.logger.error(f"❌ 報告生成失敗: {e}")
    
    def cleanup_data_job(self):
        """數據清理任務"""
        try:
            self.logger.info("🧹 開始清理舊數據...")
            
            # 清理 30 天前的數據
            self.db.cleanup_old_data(days=30)
            
            self.logger.info("✅ 數據清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據清理失敗: {e}")
    
    def update_subreddit_stats_job(self):
        """更新 subreddit 統計任務"""
        try:
            self.logger.info("📊 開始更新 Subreddit 統計...")
            
            self.reddit_collector.update_subreddit_stats()
            
            self.logger.info("✅ Subreddit 統計更新完成")
            
        except Exception as e:
            self.logger.error(f"❌ Subreddit 統計更新失敗: {e}")
    
    def quick_test_job(self):
        """快速測試任務"""
        try:
            # 獲取系統狀態
            overview = self.trend_analyzer.get_market_overview("1h")
            
            self.logger.info(f"💓 系統心跳檢查 - 活動統計: {overview['activity_stats']}")
            
        except Exception as e:
            self.logger.error(f"❌ 快速測試失敗: {e}")
    
    def _job_listener(self, event):
        """任務事件監聽器"""
        if event.exception:
            self.logger.error(f"任務 {event.job_id} 執行失敗: {event.exception}")
        else:
            self.logger.info(f"任務 {event.job_id} 執行成功")
    
    def start(self):
        """啟動調度器"""
        try:
            self.logger.info("🚀 啟動任務調度器...")
            
            # 顯示已安排的任務
            jobs = self.scheduler.get_jobs()
            self.logger.info(f"已安排 {len(jobs)} 個任務:")
            for job in jobs:
                self.logger.info(f"  - {job.name} ({job.id}): {job.trigger}")
            
            # 立即執行一次快速測試
            self.quick_test_job()
            
            # 啟動調度器
            self.scheduler.start()
            
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號，正在停止調度器...")
            self.stop()
        except Exception as e:
            self.logger.error(f"調度器啟動失敗: {e}")
            raise
    
    def stop(self):
        """停止調度器"""
        try:
            self.logger.info("⏹️ 停止任務調度器...")
            self.scheduler.shutdown(wait=True)
            self.logger.info("✅ 調度器已停止")
        except Exception as e:
            self.logger.error(f"停止調度器失敗: {e}")
    
    def run_job_now(self, job_id: str):
        """立即執行指定任務"""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                self.logger.info(f"🏃 立即執行任務: {job.name}")
                job.func()
            else:
                self.logger.error(f"未找到任務: {job_id}")
        except Exception as e:
            self.logger.error(f"執行任務 {job_id} 失敗: {e}")
    
    def get_job_status(self):
        """獲取任務狀態"""
        jobs = self.scheduler.get_jobs()
        status = []
        
        for job in jobs:
            status.append({
                'id': job.id,
                'name': job.name,
                'next_run': str(job.next_run_time) if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return status
