"""
實體提取器
從 Reddit 內容中提取股票代碼和加密貨幣符號
"""

import re
import json
import requests
import time
from typing import Dict, Any, List, Set, Optional
import logging
from ..utils.config import Config, STOCK_SYMBOLS, CRYPTO_SYMBOLS, SENTIMENT_PROMPTS
from ..storage.database import DatabaseManager
from .smart_symbol_validator import SmartSymbolValidator


class EntityExtractor:
    """實體提取器"""
    
    def __init__(self):
        self.config = Config()
        self.db = DatabaseManager()
        self.logger = self._setup_logger()
        
        # OpenRouter API 配置
        self.api_config = self.config.get_openrouter_config()
        self.headers = {
            "Authorization": f"Bearer {self.api_config['api_key']}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/reddit-research",
            "X-Title": "Reddit Stock Crypto Monitor"
        }
        
        # 編譯正則表達式
        self._compile_patterns()

        # 初始化智能驗證器
        self.smart_validator = SmartSymbolValidator()
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('EntityExtractor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _compile_patterns(self):
        """編譯正則表達式模式"""
        # 股票代碼模式 (通常是 1-5 個大寫字母)
        stock_symbols = '|'.join(STOCK_SYMBOLS.keys())
        self.stock_pattern = re.compile(
            rf'\b(?:\$)?({stock_symbols})\b',
            re.IGNORECASE
        )
        
        # 加密貨幣模式
        crypto_symbols = '|'.join(CRYPTO_SYMBOLS.keys())
        self.crypto_pattern = re.compile(
            rf'\b(?:\$)?({crypto_symbols})\b',
            re.IGNORECASE
        )
        
        # 通用股票模式 (用於發現新的股票代碼)
        self.generic_stock_pattern = re.compile(
            r'\$([A-Z]{1,5})\b|(?:stock|ticker|symbol)\s+([A-Z]{1,5})\b',
            re.IGNORECASE
        )
        
        # 通用加密貨幣模式
        self.generic_crypto_pattern = re.compile(
            r'\b([A-Z]{2,10})(?:coin|token)\b|\b(BTC|ETH|crypto|coin)\b',
            re.IGNORECASE
        )
    
    def extract_entities(self, content: str, content_id: str, content_type: str) -> List[Dict[str, Any]]:
        """從內容中提取實體"""
        entities = []
        
        if not content or len(content.strip()) < 5:
            return entities
        
        # 使用正則表達式提取
        regex_entities = self._extract_with_regex(content)
        entities.extend(regex_entities)

        # 使用 LLM 提取（更準確但成本更高）
        if len(content) > 50:  # 只對較長的內容使用 LLM
            llm_entities = self._extract_with_llm(content)
            entities.extend(llm_entities)

        # 去重和過濾
        entities = self._deduplicate_entities(entities)

        # 智能驗證過濾
        entities = self._smart_validate_entities(entities, content)
        
        # 保存到數據庫
        for entity in entities:
            self.db.insert_entity_mention(
                content_id=content_id,
                content_type=content_type,
                entity_type=entity['type'],
                symbol=entity['symbol'],
                name=entity.get('name'),
                confidence=entity['confidence'],
                context=entity.get('context', '')
            )
        
        if entities:
            self.logger.info(f"從 {content_type} {content_id} 提取了 {len(entities)} 個實體")
        
        return entities
    
    def _extract_with_regex(self, content: str) -> List[Dict[str, Any]]:
        """使用正則表達式提取實體"""
        entities = []
        
        # 提取已知股票代碼
        stock_matches = self.stock_pattern.findall(content)
        for symbol in set(stock_matches):
            symbol = symbol.upper()
            if symbol in STOCK_SYMBOLS:
                entities.append({
                    'type': 'stock',
                    'symbol': symbol,
                    'name': STOCK_SYMBOLS[symbol],
                    'confidence': 0.9,
                    'context': self._get_context(content, symbol)
                })
        
        # 提取已知加密貨幣
        crypto_matches = self.crypto_pattern.findall(content)
        for symbol in set(crypto_matches):
            symbol = symbol.upper()
            if symbol in CRYPTO_SYMBOLS:
                entities.append({
                    'type': 'crypto',
                    'symbol': symbol,
                    'name': CRYPTO_SYMBOLS[symbol],
                    'confidence': 0.9,
                    'context': self._get_context(content, symbol)
                })
        
        # 提取可能的新股票代碼
        generic_stock_matches = self.generic_stock_pattern.findall(content)
        for match in generic_stock_matches:
            symbol = (match[0] or match[1]).upper()
            if symbol and symbol not in STOCK_SYMBOLS and len(symbol) <= 5:
                entities.append({
                    'type': 'stock',
                    'symbol': symbol,
                    'name': None,
                    'confidence': 0.6,
                    'context': self._get_context(content, symbol)
                })
        
        return entities
    
    def _extract_with_llm(self, content: str) -> List[Dict[str, Any]]:
        """使用 LLM 提取實體"""
        try:
            prompt = SENTIMENT_PROMPTS['entity_extraction'].format(content=content[:800])
            response = self._call_openrouter_api(prompt)
            
            if response:
                return self._parse_entity_response(response)
        
        except Exception as e:
            self.logger.error(f"LLM 實體提取失敗: {e}")
        
        return []
    
    def _call_openrouter_api(self, prompt: str) -> Optional[Dict[str, Any]]:
        """調用 OpenRouter API"""
        try:
            payload = {
                "model": self.api_config['model'],
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 500
            }
            
            response = requests.post(
                f"{self.api_config['base_url']}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"OpenRouter API 錯誤: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"API 調用失敗: {e}")
            return None
    
    def _parse_entity_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析 LLM 響應"""
        entities = []
        
        try:
            content = response['choices'][0]['message']['content']
            
            # 嘗試解析 JSON
            if '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                json_str = content[json_start:json_end]
                
                data = json.loads(json_str)
                
                if 'entities' in data and isinstance(data['entities'], list):
                    for entity in data['entities']:
                        if all(key in entity for key in ['type', 'symbol']):
                            # 驗證實體類型
                            if entity['type'] in ['stock', 'crypto']:
                                symbol = entity['symbol'].upper()
                                
                                # 驗證符號格式
                                if re.match(r'^[A-Z0-9]{1,10}$', symbol):
                                    entities.append({
                                        'type': entity['type'],
                                        'symbol': symbol,
                                        'name': entity.get('name'),
                                        'confidence': min(1.0, max(0.0, entity.get('confidence', 0.8))),
                                        'context': entity.get('context', '')
                                    })
        
        except Exception as e:
            self.logger.error(f"解析實體響應失敗: {e}")
        
        return entities
    
    def _get_context(self, content: str, symbol: str) -> str:
        """獲取實體提及的上下文"""
        # 找到符號在文本中的位置
        pattern = re.compile(rf'\b{re.escape(symbol)}\b', re.IGNORECASE)
        match = pattern.search(content)
        
        if match:
            start = max(0, match.start() - 30)
            end = min(len(content), match.end() + 30)
            context = content[start:end].strip()
            return context
        
        return ""
    
    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重實體"""
        seen = set()
        unique_entities = []
        
        for entity in entities:
            key = (entity['type'], entity['symbol'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)
            else:
                # 如果重複，保留信心度更高的
                for i, existing in enumerate(unique_entities):
                    if (existing['type'], existing['symbol']) == key:
                        if entity['confidence'] > existing['confidence']:
                            unique_entities[i] = entity
                        break
        
        return unique_entities

    def _smart_validate_entities(self, entities: List[Dict[str, Any]], content: str) -> List[Dict[str, Any]]:
        """使用智能驗證器過濾實體"""
        validated_entities = []

        for entity in entities:
            symbol = entity['symbol']
            entity_type = entity['type']

            # 使用智能驗證器驗證
            is_valid, confidence, reason = self.smart_validator.validate_symbol(
                symbol, content, entity_type
            )

            if is_valid and confidence > 0.5:
                # 更新實體的信心度
                entity['confidence'] = min(entity['confidence'], confidence)
                entity['validation_reason'] = reason
                validated_entities.append(entity)

                self.logger.info(f"✅ 驗證通過: {symbol} ({entity_type}) - 信心度: {confidence:.2f}, 原因: {reason}")
            else:
                self.logger.info(f"❌ 驗證失敗: {symbol} ({entity_type}) - 信心度: {confidence:.2f}, 原因: {reason}")

        return validated_entities
    
    def extract_from_recent_posts(self, hours: int = 24) -> int:
        """從最近的帖子中提取實體"""
        posts = self.db.models.get_recent_posts(hours=hours)
        extracted_count = 0
        
        for post in posts:
            # 檢查是否已經提取過
            if not self._is_already_extracted(post['id'], 'post'):
                full_content = f"{post['title']} {post['content']}"
                
                entities = self.extract_entities(
                    content=full_content,
                    content_id=post['id'],
                    content_type='post'
                )
                
                extracted_count += len(entities)
                time.sleep(0.5)  # 避免 API 限制
        
        self.logger.info(f"從 {len(posts)} 個帖子中提取了 {extracted_count} 個實體")
        return extracted_count
    
    def extract_from_recent_comments(self, hours: int = 24) -> int:
        """從最近的評論中提取實體"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, content FROM comments 
            WHERE created_utc >= datetime('now', '-{} hours')
            AND LENGTH(content) > 10
            ORDER BY created_utc DESC
            LIMIT 200
        '''.format(hours))
        
        comments = cursor.fetchall()
        conn.close()
        
        extracted_count = 0
        
        for comment_id, content in comments:
            # 檢查是否已經提取過
            if not self._is_already_extracted(comment_id, 'comment'):
                entities = self.extract_entities(
                    content=content,
                    content_id=comment_id,
                    content_type='comment'
                )
                
                extracted_count += len(entities)
                time.sleep(0.5)  # 避免 API 限制
        
        self.logger.info(f"從 {len(comments)} 個評論中提取了 {extracted_count} 個實體")
        return extracted_count
    
    def _is_already_extracted(self, content_id: str, content_type: str) -> bool:
        """檢查是否已經提取過實體"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) FROM entities 
            WHERE content_id = ? AND content_type = ?
        ''', (content_id, content_type))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
    
    def get_trending_entities(self, entity_type: str = None, hours: int = 24, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取趨勢實體"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        if entity_type:
            cursor.execute('''
                SELECT symbol, entity_type, COUNT(*) as mention_count,
                       AVG(confidence) as avg_confidence
                FROM entities 
                WHERE entity_type = ? AND extracted_at >= datetime('now', '-{} hours')
                GROUP BY symbol, entity_type
                ORDER BY mention_count DESC
                LIMIT ?
            '''.format(hours), (entity_type, limit))
        else:
            cursor.execute('''
                SELECT symbol, entity_type, COUNT(*) as mention_count,
                       AVG(confidence) as avg_confidence
                FROM entities 
                WHERE extracted_at >= datetime('now', '-{} hours')
                GROUP BY symbol, entity_type
                ORDER BY mention_count DESC
                LIMIT ?
            '''.format(hours), (limit,))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
