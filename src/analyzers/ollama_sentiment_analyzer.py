"""
Ollama 情緒分析器
使用本地 Ollama Qwen3:14B 模型分析 Reddit 內容的情緒
"""

import requests
import json
import time
import re
from typing import Dict, Any, List, Optional
import logging
from ..utils.config import Config, SENTIMENT_PROMPTS
from ..storage.database import DatabaseManager


class OllamaSentimentAnalyzer:
    """基於 Ollama 的情緒分析器"""
    
    def __init__(self):
        self.config = Config()
        self.db = DatabaseManager()
        self.logger = self._setup_logger()
        
        # Ollama 配置
        self.ollama_url = "http://localhost:11434"
        self.model_name = "qwen3:14b"
        
        # 測試連接
        self._test_ollama_connection()
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('OllamaSentimentAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _test_ollama_connection(self):
        """測試 Ollama 連接"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                if self.model_name in model_names:
                    self.logger.info(f"✅ Ollama 連接成功，模型 {self.model_name} 可用")
                else:
                    self.logger.warning(f"⚠️ 模型 {self.model_name} 不可用，可用模型: {model_names}")
            else:
                self.logger.error("❌ Ollama 服務連接失敗")
        except Exception as e:
            self.logger.error(f"❌ Ollama 連接測試失敗: {e}")
    
    def analyze_sentiment(self, content: str, content_id: str, content_type: str) -> Optional[Dict[str, Any]]:
        """分析單個內容的情緒"""
        if not content or len(content.strip()) < 10:
            return None
        
        try:
            # 準備提示詞
            prompt = self._create_sentiment_prompt(content[:1500])  # 限制長度
            
            # 調用 Ollama API
            response = self._call_ollama_api(prompt)
            
            if response:
                # 解析響應
                sentiment_data = self._parse_sentiment_response(response)
                
                if sentiment_data:
                    # 保存到數據庫
                    self.db.models.insert_sentiment(
                        content_id=content_id,
                        content_type=content_type,
                        sentiment_score=sentiment_data['sentiment_score'],
                        sentiment_label=sentiment_data['sentiment_label'],
                        confidence=sentiment_data['confidence'],
                        analysis_model=f"ollama/{self.model_name}",
                        raw_response=json.dumps(response)
                    )
                    
                    self.logger.info(f"分析完成: {content_type} {content_id} - {sentiment_data['sentiment_label']} ({sentiment_data['sentiment_score']:.2f})")
                    return sentiment_data
            
        except Exception as e:
            self.logger.error(f"情緒分析失敗: {e}")
        
        return None
    
    def _create_sentiment_prompt(self, content: str) -> str:
        """創建情緒分析提示詞"""
        return f"""請分析以下 Reddit 內容的情緒，特別關注股票和加密貨幣相關的情緒。

內容: "{content}"

請以 JSON 格式回應，包含以下字段：
{{
    "sentiment_score": <-1.0 到 1.0 之間的數值，負數表示負面，正數表示正面>,
    "sentiment_label": <"positive", "negative", 或 "neutral">,
    "confidence": <0.0 到 1.0 之間的信心度>,
    "explanation": <簡短解釋分析原因>
}}

關鍵評判標準：
- 正面詞彙：買入、看漲、月亮、好、優秀、上漲、持有
- 負面詞彙：賣出、看跌、崩盤、糟糕、下跌、賠錢
- 中性：僅陳述事實，無明顯情緒傾向

只回應 JSON，不要其他文字。"""
    
    def _call_ollama_api(self, prompt: str) -> Optional[Dict[str, Any]]:
        """調用 Ollama API"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "num_predict": 200
                }
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Ollama API 錯誤: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"API 調用失敗: {e}")
            return None
    
    def _parse_sentiment_response(self, response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析 API 響應"""
        try:
            content = response.get('response', '')
            
            # 嘗試解析 JSON
            if '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                json_str = content[json_start:json_end]

                # 清理 JSON 字符串
                json_str = json_str.replace('\n', ' ').replace('\r', ' ')

                try:
                    sentiment_data = json.loads(json_str)
                except json.JSONDecodeError:
                    # 如果 JSON 解析失敗，嘗試修復常見問題
                    json_str = self._fix_json_string(json_str)
                    sentiment_data = json.loads(json_str)

                # 驗證必要字段
                required_fields = ['sentiment_score', 'sentiment_label', 'confidence']
                if all(field in sentiment_data for field in required_fields):
                    # 確保數值在正確範圍內
                    sentiment_data['sentiment_score'] = max(-1.0, min(1.0, float(sentiment_data['sentiment_score'])))
                    sentiment_data['confidence'] = max(0.0, min(1.0, float(sentiment_data['confidence'])))

                    # 標準化情緒標籤
                    label = str(sentiment_data['sentiment_label']).lower()
                    if label in ['positive', 'negative', 'neutral']:
                        sentiment_data['sentiment_label'] = label
                    else:
                        # 根據分數推斷標籤
                        score = sentiment_data['sentiment_score']
                        if score > 0.1:
                            sentiment_data['sentiment_label'] = 'positive'
                        elif score < -0.1:
                            sentiment_data['sentiment_label'] = 'negative'
                        else:
                            sentiment_data['sentiment_label'] = 'neutral'

                    return sentiment_data

            # 如果 JSON 解析失敗，嘗試從文本中提取信息
            return self._extract_sentiment_from_text(content)

        except Exception as e:
            self.logger.error(f"解析響應失敗: {e}")
            # 返回默認值
            return {
                'sentiment_score': 0.0,
                'sentiment_label': 'neutral',
                'confidence': 0.5,
                'explanation': 'Failed to parse response'
            }

        return None

    def _fix_json_string(self, json_str: str) -> str:
        """修復常見的 JSON 格式問題"""
        # 移除多餘的逗號
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        # 確保字符串值被正確引用
        json_str = re.sub(r':\s*([^",\{\}\[\]]+)(?=\s*[,\}])', r': "\1"', json_str)

        return json_str

    def _extract_sentiment_from_text(self, content: str) -> Optional[Dict[str, Any]]:
        """從文本中提取情緒信息（備用方法）"""
        content_lower = content.lower()

        # 簡單的關鍵詞匹配
        positive_words = ['positive', 'bullish', 'good', 'great', 'excellent', 'buy', 'moon', 'up', 'gain']
        negative_words = ['negative', 'bearish', 'bad', 'terrible', 'sell', 'crash', 'dump', 'down', 'loss']

        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)

        if positive_count > negative_count:
            return {
                'sentiment_score': 0.6,
                'sentiment_label': 'positive',
                'confidence': 0.7,
                'explanation': 'Extracted from text analysis'
            }
        elif negative_count > positive_count:
            return {
                'sentiment_score': -0.6,
                'sentiment_label': 'negative',
                'confidence': 0.7,
                'explanation': 'Extracted from text analysis'
            }
        else:
            return {
                'sentiment_score': 0.0,
                'sentiment_label': 'neutral',
                'confidence': 0.5,
                'explanation': 'Extracted from text analysis'
            }
    
    def analyze_batch(self, contents: List[Dict[str, Any]], batch_size: int = 5) -> List[Dict[str, Any]]:
        """批量分析情緒"""
        results = []
        
        for i in range(0, len(contents), batch_size):
            batch = contents[i:i + batch_size]
            
            for item in batch:
                result = self.analyze_sentiment(
                    content=item['content'],
                    content_id=item['content_id'],
                    content_type=item['content_type']
                )
                
                if result:
                    results.append({
                        'content_id': item['content_id'],
                        'content_type': item['content_type'],
                        **result
                    })
                
                # 本地模型不需要太長延遲
                time.sleep(0.1)
            
            # 批次間短暫暫停
            if i + batch_size < len(contents):
                time.sleep(0.5)
        
        self.logger.info(f"批量分析完成: {len(results)}/{len(contents)} 個項目")
        return results
    
    def analyze_recent_posts(self, hours: int = 24) -> int:
        """分析最近的帖子"""
        posts = self.db.models.get_recent_posts(hours=hours)
        analyzed_count = 0
        
        for post in posts:
            # 檢查是否已經分析過
            if not self._is_already_analyzed(post['id'], 'post'):
                # 分析標題和內容
                full_content = f"{post['title']} {post['content']}"
                
                result = self.analyze_sentiment(
                    content=full_content,
                    content_id=post['id'],
                    content_type='post'
                )
                
                if result:
                    analyzed_count += 1
                
                time.sleep(0.2)  # 本地模型較快
        
        self.logger.info(f"分析了 {analyzed_count} 個新帖子")
        return analyzed_count
    
    def analyze_recent_comments(self, hours: int = 24) -> int:
        """分析最近的評論"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, content FROM comments 
            WHERE created_utc >= datetime('now', '-{} hours')
            AND LENGTH(content) > 10
            ORDER BY created_utc DESC
            LIMIT 200
        '''.format(hours))
        
        comments = cursor.fetchall()
        conn.close()
        
        analyzed_count = 0
        
        for comment_id, content in comments:
            # 檢查是否已經分析過
            if not self._is_already_analyzed(comment_id, 'comment'):
                result = self.analyze_sentiment(
                    content=content,
                    content_id=comment_id,
                    content_type='comment'
                )
                
                if result:
                    analyzed_count += 1
                
                time.sleep(0.2)  # 本地模型較快
        
        self.logger.info(f"分析了 {analyzed_count} 個新評論")
        return analyzed_count
    
    def _is_already_analyzed(self, content_id: str, content_type: str) -> bool:
        """檢查內容是否已經分析過"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) FROM sentiment_analysis 
            WHERE content_id = ? AND content_type = ?
        ''', (content_id, content_type))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
    
    def get_sentiment_stats(self, hours: int = 24) -> Dict[str, Any]:
        """獲取情緒統計"""
        return self.db.get_sentiment_summary(hours=hours)
    
    def test_api_connection(self) -> bool:
        """測試 Ollama API 連接"""
        try:
            test_prompt = "請回應 'API 連接正常' 這句話。"
            response = self._call_ollama_api(test_prompt)
            
            if response and 'response' in response:
                self.logger.info("✅ Ollama API 連接成功")
                return True
            else:
                self.logger.error("❌ Ollama API 連接失敗")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ API 測試失敗: {e}")
            return False