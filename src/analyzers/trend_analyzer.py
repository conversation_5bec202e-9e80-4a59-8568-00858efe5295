"""
趨勢分析器
分析股票和加密貨幣的趨勢數據
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import logging
import statistics
from ..storage.database import DatabaseManager


class TrendAnalyzer:
    """趨勢分析器"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('TrendAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def calculate_all_trends(self) -> Dict[str, int]:
        """計算所有時間段的趨勢"""
        results = {}
        
        # 計算不同時間段的趨勢
        time_periods = ["1h", "6h", "24h", "7d"]
        
        for period in time_periods:
            success = self.db.calculate_trends(period)
            if success:
                results[period] = self._count_trends(period)
            else:
                results[period] = 0
        
        self.logger.info(f"趨勢計算完成: {results}")
        return results
    
    def _count_trends(self, time_period: str) -> int:
        """計算指定時間段的趨勢數量"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) FROM trends 
            WHERE time_period = ? AND calculated_at >= datetime('now', '-1 hour')
        ''', (time_period,))
        
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def get_top_trending(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取熱門趨勢（按趨勢分數排序）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                GROUP BY entity_type, symbol
                ORDER BY trend_score DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE time_period = ?
                GROUP BY entity_type, symbol
                ORDER BY trend_score DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_trending_by_mentions(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取熱門趨勢（按提及次數排序）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                GROUP BY entity_type, symbol
                ORDER BY mention_count DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE time_period = ?
                GROUP BY entity_type, symbol
                ORDER BY mention_count DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_trending_by_momentum(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取突然爆紅的項目（提及次數突然增加）"""
        hours = self._time_period_to_hours(time_period)

        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        # 比較當前時段和前一時段的提及次數
        if entity_type:
            cursor.execute('''
                SELECT
                    t1.entity_type, t1.symbol,
                    t1.mention_count as current_mentions,
                    COALESCE(t2.mention_count, 0) as previous_mentions,
                    t1.avg_sentiment_score,
                    (t1.mention_count * 1.0 / NULLIF(COALESCE(t2.mention_count, 1), 0)) as momentum_ratio
                FROM trends t1
                LEFT JOIN trends t2 ON t1.symbol = t2.symbol
                    AND t1.entity_type = t2.entity_type
                    AND t2.calculated_at < datetime('now', '-{} hours')
                    AND t2.calculated_at >= datetime('now', '-{} hours')
                WHERE t1.entity_type = ?
                    AND t1.time_period = ?
                    AND t1.calculated_at >= datetime('now', '-1 hour')
                    AND t1.mention_count >= 3
                GROUP BY t1.entity_type, t1.symbol
                ORDER BY momentum_ratio DESC
                LIMIT ?
            '''.format(hours, hours * 2), (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT
                    t1.entity_type, t1.symbol,
                    t1.mention_count as current_mentions,
                    COALESCE(t2.mention_count, 0) as previous_mentions,
                    t1.avg_sentiment_score,
                    (t1.mention_count * 1.0 / NULLIF(COALESCE(t2.mention_count, 1), 0)) as momentum_ratio
                FROM trends t1
                LEFT JOIN trends t2 ON t1.symbol = t2.symbol
                    AND t1.entity_type = t2.entity_type
                    AND t2.calculated_at < datetime('now', '-{} hours')
                    AND t2.calculated_at >= datetime('now', '-{} hours')
                WHERE t1.time_period = ?
                    AND t1.calculated_at >= datetime('now', '-1 hour')
                    AND t1.mention_count >= 3
                GROUP BY t1.entity_type, t1.symbol
                ORDER BY momentum_ratio DESC
                LIMIT ?
            '''.format(hours, hours * 2), (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_niche_favorites(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取小眾熱門（提及次數不多但情緒很高）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                    AND mention_count BETWEEN 3 AND 15
                    AND avg_sentiment_score > 0.3
                GROUP BY entity_type, symbol
                ORDER BY avg_sentiment_score DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE time_period = ?
                    AND mention_count BETWEEN 3 AND 15
                    AND avg_sentiment_score > 0.3
                GROUP BY entity_type, symbol
                ORDER BY avg_sentiment_score DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_controversial(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取爭議項目（情緒分化嚴重）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (positive_sentiment_count + negative_sentiment_count) as polarized_count,
                       ABS(positive_sentiment_count - negative_sentiment_count) as controversy_score
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                    AND mention_count >= 5
                    AND positive_sentiment_count > 0
                    AND negative_sentiment_count > 0
                GROUP BY entity_type, symbol
                ORDER BY controversy_score DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (positive_sentiment_count + negative_sentiment_count) as polarized_count,
                       ABS(positive_sentiment_count - negative_sentiment_count) as controversy_score
                FROM trends
                WHERE time_period = ?
                    AND mention_count >= 5
                    AND positive_sentiment_count > 0
                    AND negative_sentiment_count > 0
                GROUP BY entity_type, symbol
                ORDER BY controversy_score DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_emerging_trends(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取新興趨勢（最近才開始被討論）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        # 查找在當前時段有討論但在更早時段沒有討論的項目
        hours = self._time_period_to_hours(time_period)

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT t1.entity_type, t1.symbol, t1.mention_count,
                       t1.positive_sentiment_count, t1.negative_sentiment_count, t1.neutral_sentiment_count,
                       t1.avg_sentiment_score, t1.trend_score, t1.calculated_at
                FROM trends t1
                LEFT JOIN trends t2 ON t1.symbol = t2.symbol
                    AND t1.entity_type = t2.entity_type
                    AND t2.calculated_at < datetime('now', '-{} hours')
                WHERE t1.entity_type = ?
                    AND t1.time_period = ?
                    AND t1.calculated_at >= datetime('now', '-1 hour')
                    AND t1.mention_count >= 3
                    AND t2.symbol IS NULL
                GROUP BY t1.entity_type, t1.symbol
                ORDER BY t1.mention_count DESC
                LIMIT ?
            '''.format(hours), (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT t1.entity_type, t1.symbol, t1.mention_count,
                       t1.positive_sentiment_count, t1.negative_sentiment_count, t1.neutral_sentiment_count,
                       t1.avg_sentiment_score, t1.trend_score, t1.calculated_at
                FROM trends t1
                LEFT JOIN trends t2 ON t1.symbol = t2.symbol
                    AND t1.entity_type = t2.entity_type
                    AND t2.calculated_at < datetime('now', '-{} hours')
                WHERE t1.time_period = ?
                    AND t1.calculated_at >= datetime('now', '-1 hour')
                    AND t1.mention_count >= 3
                    AND t2.symbol IS NULL
                GROUP BY t1.entity_type, t1.symbol
                ORDER BY t1.mention_count DESC
                LIMIT ?
            '''.format(hours), (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_meme_potential(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取 Meme 潛力項目（基於特定特徵）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        # Meme 特徵：高情緒波動、快速增長、社區驅動
        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (positive_sentiment_count * 1.0 / NULLIF(mention_count, 0)) as positive_ratio,
                       (mention_count * avg_sentiment_score) as meme_score
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                    AND mention_count >= 5
                    AND avg_sentiment_score > 0.2
                    AND positive_sentiment_count > negative_sentiment_count
                GROUP BY entity_type, symbol
                ORDER BY meme_score DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (positive_sentiment_count * 1.0 / NULLIF(mention_count, 0)) as positive_ratio,
                       (mention_count * avg_sentiment_score) as meme_score
                FROM trends
                WHERE time_period = ?
                    AND mention_count >= 5
                    AND avg_sentiment_score > 0.2
                    AND positive_sentiment_count > negative_sentiment_count
                GROUP BY entity_type, symbol
                ORDER BY meme_score DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_small_cap_gems(self, entity_type: str = None, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取小市值寶石（低提及但高情緒的潛力項目）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        if entity_type:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (avg_sentiment_score * positive_sentiment_count) as gem_score
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                    AND mention_count BETWEEN 2 AND 8
                    AND avg_sentiment_score > 0.4
                    AND positive_sentiment_count >= negative_sentiment_count
                GROUP BY entity_type, symbol
                ORDER BY gem_score DESC
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute('''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at,
                       (avg_sentiment_score * positive_sentiment_count) as gem_score
                FROM trends
                WHERE time_period = ?
                    AND mention_count BETWEEN 2 AND 8
                    AND avg_sentiment_score > 0.4
                    AND positive_sentiment_count >= negative_sentiment_count
                GROUP BY entity_type, symbol
                ORDER BY gem_score DESC
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_sentiment_leaders(self, entity_type: str = None, time_period: str = "24h", limit: int = 10, sentiment_type: str = "positive") -> List[Dict[str, Any]]:
        """獲取情緒領導者（最正面或最負面）"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()

        order_by = "avg_sentiment_score DESC" if sentiment_type == "positive" else "avg_sentiment_score ASC"
        sentiment_filter = "avg_sentiment_score > 0.1" if sentiment_type == "positive" else "avg_sentiment_score < -0.1"

        if entity_type:
            cursor.execute(f'''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE entity_type = ? AND time_period = ?
                    AND mention_count >= 3
                    AND {sentiment_filter}
                GROUP BY entity_type, symbol
                ORDER BY {order_by}
                LIMIT ?
            ''', (entity_type, time_period, limit))
        else:
            cursor.execute(f'''
                SELECT DISTINCT entity_type, symbol, mention_count,
                       positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                       avg_sentiment_score, trend_score, calculated_at
                FROM trends
                WHERE time_period = ?
                    AND mention_count >= 3
                    AND {sentiment_filter}
                GROUP BY entity_type, symbol
                ORDER BY {order_by}
                LIMIT ?
            ''', (time_period, limit))

        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results
    
    def get_sentiment_trends(self, symbol: str, entity_type: str, hours: int = 24) -> Dict[str, Any]:
        """獲取特定實體的情緒趨勢"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        # 獲取時間序列情緒數據
        cursor.execute('''
            SELECT 
                datetime(sa.analyzed_at, 'start of hour') as hour,
                AVG(sa.sentiment_score) as avg_sentiment,
                COUNT(*) as mention_count,
                SUM(CASE WHEN sa.sentiment_label = 'positive' THEN 1 ELSE 0 END) as positive_count,
                SUM(CASE WHEN sa.sentiment_label = 'negative' THEN 1 ELSE 0 END) as negative_count,
                SUM(CASE WHEN sa.sentiment_label = 'neutral' THEN 1 ELSE 0 END) as neutral_count
            FROM sentiment_analysis sa
            JOIN entities e ON sa.content_id = e.content_id AND sa.content_type = e.content_type
            WHERE e.symbol = ? AND e.entity_type = ?
            AND sa.analyzed_at >= datetime('now', '-{} hours')
            GROUP BY hour
            ORDER BY hour
        '''.format(hours), (symbol, entity_type))
        
        hourly_data = cursor.fetchall()
        
        # 獲取總體統計
        cursor.execute('''
            SELECT 
                AVG(sa.sentiment_score) as overall_sentiment,
                COUNT(*) as total_mentions,
                SUM(CASE WHEN sa.sentiment_label = 'positive' THEN 1 ELSE 0 END) as total_positive,
                SUM(CASE WHEN sa.sentiment_label = 'negative' THEN 1 ELSE 0 END) as total_negative,
                SUM(CASE WHEN sa.sentiment_label = 'neutral' THEN 1 ELSE 0 END) as total_neutral
            FROM sentiment_analysis sa
            JOIN entities e ON sa.content_id = e.content_id AND sa.content_type = e.content_type
            WHERE e.symbol = ? AND e.entity_type = ?
            AND sa.analyzed_at >= datetime('now', '-{} hours')
        '''.format(hours), (symbol, entity_type))
        
        overall_stats = cursor.fetchone()
        conn.close()
        
        # 計算趨勢方向
        sentiment_scores = [row[1] for row in hourly_data if row[1] is not None]
        trend_direction = self._calculate_trend_direction(sentiment_scores)
        
        return {
            'symbol': symbol,
            'entity_type': entity_type,
            'time_period': f'{hours}h',
            'hourly_data': [
                {
                    'hour': row[0],
                    'avg_sentiment': row[1],
                    'mention_count': row[2],
                    'positive_count': row[3],
                    'negative_count': row[4],
                    'neutral_count': row[5]
                }
                for row in hourly_data
            ],
            'overall_sentiment': overall_stats[0] if overall_stats[0] else 0.0,
            'total_mentions': overall_stats[1] if overall_stats[1] else 0,
            'sentiment_distribution': {
                'positive': overall_stats[2] if overall_stats[2] else 0,
                'negative': overall_stats[3] if overall_stats[3] else 0,
                'neutral': overall_stats[4] if overall_stats[4] else 0
            },
            'trend_direction': trend_direction
        }
    
    def _calculate_trend_direction(self, sentiment_scores: List[float]) -> str:
        """計算趨勢方向"""
        if len(sentiment_scores) < 2:
            return "insufficient_data"
        
        # 使用線性回歸計算趨勢
        n = len(sentiment_scores)
        x_values = list(range(n))
        
        # 計算斜率
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(sentiment_scores)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, sentiment_scores))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            return "stable"
        
        slope = numerator / denominator
        
        # 判斷趨勢方向
        if slope > 0.01:
            return "improving"
        elif slope < -0.01:
            return "declining"
        else:
            return "stable"
    
    def get_market_overview(self, time_period: str = "24h") -> Dict[str, Any]:
        """獲取市場概覽"""
        # 獲取各種排名
        top_stocks = self.get_top_trending("stock", time_period, 10)
        top_cryptos = self.get_top_trending("crypto", time_period, 10)

        # 新增排名
        momentum_stocks = self.get_trending_by_momentum("stock", time_period, 5)
        momentum_cryptos = self.get_trending_by_momentum("crypto", time_period, 5)

        niche_stocks = self.get_niche_favorites("stock", time_period, 5)
        niche_cryptos = self.get_niche_favorites("crypto", time_period, 5)

        controversial_stocks = self.get_controversial("stock", time_period, 5)
        controversial_cryptos = self.get_controversial("crypto", time_period, 5)

        emerging_stocks = self.get_emerging_trends("stock", time_period, 5)
        emerging_cryptos = self.get_emerging_trends("crypto", time_period, 5)

        meme_stocks = self.get_meme_potential("stock", time_period, 5)
        meme_cryptos = self.get_meme_potential("crypto", time_period, 5)

        gem_stocks = self.get_small_cap_gems("stock", time_period, 5)
        gem_cryptos = self.get_small_cap_gems("crypto", time_period, 5)

        # 情緒領導者
        positive_stocks = self.get_sentiment_leaders("stock", time_period, 5, "positive")
        negative_stocks = self.get_sentiment_leaders("stock", time_period, 5, "negative")
        positive_cryptos = self.get_sentiment_leaders("crypto", time_period, 5, "positive")
        negative_cryptos = self.get_sentiment_leaders("crypto", time_period, 5, "negative")

        # 計算市場情緒
        market_sentiment = self._calculate_market_sentiment(time_period)

        # 獲取活躍度統計
        activity_stats = self._get_activity_stats(time_period)

        return {
            'time_period': time_period,
            'generated_at': datetime.now().isoformat(),
            'top_stocks': top_stocks,
            'top_cryptos': top_cryptos,
            'momentum_stocks': momentum_stocks,
            'momentum_cryptos': momentum_cryptos,
            'niche_stocks': niche_stocks,
            'niche_cryptos': niche_cryptos,
            'controversial_stocks': controversial_stocks,
            'controversial_cryptos': controversial_cryptos,
            'emerging_stocks': emerging_stocks,
            'emerging_cryptos': emerging_cryptos,
            'meme_stocks': meme_stocks,
            'meme_cryptos': meme_cryptos,
            'gem_stocks': gem_stocks,
            'gem_cryptos': gem_cryptos,
            'positive_stocks': positive_stocks,
            'negative_stocks': negative_stocks,
            'positive_cryptos': positive_cryptos,
            'negative_cryptos': negative_cryptos,
            'market_sentiment': market_sentiment,
            'activity_stats': activity_stats
        }
    
    def _calculate_market_sentiment(self, time_period: str) -> Dict[str, Any]:
        """計算整體市場情緒"""
        hours = self._time_period_to_hours(time_period)
        
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        # 股票市場情緒
        cursor.execute('''
            SELECT 
                AVG(sa.sentiment_score) as avg_sentiment,
                COUNT(*) as total_mentions,
                SUM(CASE WHEN sa.sentiment_label = 'positive' THEN 1 ELSE 0 END) as positive_count,
                SUM(CASE WHEN sa.sentiment_label = 'negative' THEN 1 ELSE 0 END) as negative_count
            FROM sentiment_analysis sa
            JOIN entities e ON sa.content_id = e.content_id AND sa.content_type = e.content_type
            WHERE e.entity_type = 'stock'
            AND sa.analyzed_at >= datetime('now', '-{} hours')
        '''.format(hours))
        
        stock_sentiment = cursor.fetchone()
        
        # 加密貨幣市場情緒
        cursor.execute('''
            SELECT 
                AVG(sa.sentiment_score) as avg_sentiment,
                COUNT(*) as total_mentions,
                SUM(CASE WHEN sa.sentiment_label = 'positive' THEN 1 ELSE 0 END) as positive_count,
                SUM(CASE WHEN sa.sentiment_label = 'negative' THEN 1 ELSE 0 END) as negative_count
            FROM sentiment_analysis sa
            JOIN entities e ON sa.content_id = e.content_id AND sa.content_type = e.content_type
            WHERE e.entity_type = 'crypto'
            AND sa.analyzed_at >= datetime('now', '-{} hours')
        '''.format(hours))
        
        crypto_sentiment = cursor.fetchone()
        conn.close()
        
        return {
            'stocks': {
                'avg_sentiment': stock_sentiment[0] if stock_sentiment[0] else 0.0,
                'total_mentions': stock_sentiment[1] if stock_sentiment[1] else 0,
                'positive_ratio': (stock_sentiment[2] / stock_sentiment[1]) if stock_sentiment[1] > 0 else 0,
                'negative_ratio': (stock_sentiment[3] / stock_sentiment[1]) if stock_sentiment[1] > 0 else 0
            },
            'crypto': {
                'avg_sentiment': crypto_sentiment[0] if crypto_sentiment[0] else 0.0,
                'total_mentions': crypto_sentiment[1] if crypto_sentiment[1] else 0,
                'positive_ratio': (crypto_sentiment[2] / crypto_sentiment[1]) if crypto_sentiment[1] > 0 else 0,
                'negative_ratio': (crypto_sentiment[3] / crypto_sentiment[1]) if crypto_sentiment[1] > 0 else 0
            }
        }
    
    def _get_activity_stats(self, time_period: str) -> Dict[str, Any]:
        """獲取活動統計"""
        hours = self._time_period_to_hours(time_period)
        
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        # 帖子統計
        cursor.execute('''
            SELECT COUNT(*) FROM posts 
            WHERE created_utc >= datetime('now', '-{} hours')
        '''.format(hours))
        posts_count = cursor.fetchone()[0]
        
        # 評論統計
        cursor.execute('''
            SELECT COUNT(*) FROM comments 
            WHERE created_utc >= datetime('now', '-{} hours')
        '''.format(hours))
        comments_count = cursor.fetchone()[0]
        
        # 實體提及統計
        cursor.execute('''
            SELECT COUNT(*) FROM entities 
            WHERE extracted_at >= datetime('now', '-{} hours')
        '''.format(hours))
        entities_count = cursor.fetchone()[0]
        
        # 情緒分析統計
        cursor.execute('''
            SELECT COUNT(*) FROM sentiment_analysis 
            WHERE analyzed_at >= datetime('now', '-{} hours')
        '''.format(hours))
        sentiment_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'posts_collected': posts_count,
            'comments_collected': comments_count,
            'entities_extracted': entities_count,
            'sentiments_analyzed': sentiment_count
        }
    
    def _time_period_to_hours(self, time_period: str) -> int:
        """將時間段轉換為小時數"""
        if time_period == "1h":
            return 1
        elif time_period == "6h":
            return 6
        elif time_period == "24h":
            return 24
        elif time_period == "7d":
            return 168
        else:
            return 24
    
    def detect_anomalies(self, entity_type: str = None, threshold: float = 2.0) -> List[Dict[str, Any]]:
        """檢測異常趨勢"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        # 獲取最近24小時和前24小時的數據進行比較
        if entity_type:
            cursor.execute('''
                SELECT symbol, entity_type,
                       SUM(CASE WHEN calculated_at >= datetime('now', '-24 hours') THEN mention_count ELSE 0 END) as recent_mentions,
                       SUM(CASE WHEN calculated_at >= datetime('now', '-48 hours') AND calculated_at < datetime('now', '-24 hours') THEN mention_count ELSE 0 END) as previous_mentions,
                       AVG(CASE WHEN calculated_at >= datetime('now', '-24 hours') THEN avg_sentiment_score ELSE NULL END) as recent_sentiment,
                       AVG(CASE WHEN calculated_at >= datetime('now', '-48 hours') AND calculated_at < datetime('now', '-24 hours') THEN avg_sentiment_score ELSE NULL END) as previous_sentiment
                FROM trends 
                WHERE entity_type = ? AND time_period = '24h'
                AND calculated_at >= datetime('now', '-48 hours')
                GROUP BY symbol, entity_type
                HAVING recent_mentions > 0 AND previous_mentions > 0
            ''', (entity_type,))
        else:
            cursor.execute('''
                SELECT symbol, entity_type,
                       SUM(CASE WHEN calculated_at >= datetime('now', '-24 hours') THEN mention_count ELSE 0 END) as recent_mentions,
                       SUM(CASE WHEN calculated_at >= datetime('now', '-48 hours') AND calculated_at < datetime('now', '-24 hours') THEN mention_count ELSE 0 END) as previous_mentions,
                       AVG(CASE WHEN calculated_at >= datetime('now', '-24 hours') THEN avg_sentiment_score ELSE NULL END) as recent_sentiment,
                       AVG(CASE WHEN calculated_at >= datetime('now', '-48 hours') AND calculated_at < datetime('now', '-24 hours') THEN avg_sentiment_score ELSE NULL END) as previous_sentiment
                FROM trends 
                WHERE time_period = '24h'
                AND calculated_at >= datetime('now', '-48 hours')
                GROUP BY symbol, entity_type
                HAVING recent_mentions > 0 AND previous_mentions > 0
            ''')
        
        data = cursor.fetchall()
        conn.close()
        
        anomalies = []
        
        for row in data:
            symbol, entity_type, recent_mentions, previous_mentions, recent_sentiment, previous_sentiment = row
            
            # 計算提及次數變化率
            mention_change_ratio = recent_mentions / previous_mentions if previous_mentions > 0 else 0
            
            # 計算情緒變化
            sentiment_change = (recent_sentiment or 0) - (previous_sentiment or 0)
            
            # 檢測異常
            is_anomaly = False
            anomaly_type = []
            
            if mention_change_ratio >= threshold:
                is_anomaly = True
                anomaly_type.append("mention_spike")
            
            if mention_change_ratio <= (1 / threshold):
                is_anomaly = True
                anomaly_type.append("mention_drop")
            
            if abs(sentiment_change) >= 0.3:
                is_anomaly = True
                if sentiment_change > 0:
                    anomaly_type.append("sentiment_improvement")
                else:
                    anomaly_type.append("sentiment_decline")
            
            if is_anomaly:
                anomalies.append({
                    'symbol': symbol,
                    'entity_type': entity_type,
                    'anomaly_types': anomaly_type,
                    'mention_change_ratio': mention_change_ratio,
                    'sentiment_change': sentiment_change,
                    'recent_mentions': recent_mentions,
                    'previous_mentions': previous_mentions,
                    'recent_sentiment': recent_sentiment,
                    'previous_sentiment': previous_sentiment
                })
        
        # 按變化幅度排序
        anomalies.sort(key=lambda x: abs(x['mention_change_ratio'] - 1) + abs(x['sentiment_change']), reverse=True)
        
        self.logger.info(f"檢測到 {len(anomalies)} 個異常趨勢")
        return anomalies
