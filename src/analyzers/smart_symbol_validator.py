"""
智能股票/加密貨幣符號驗證器
使用多層驗證策略來準確識別真實的金融符號
"""

import re
import requests
import time
import json
from typing import Dict, Any, List, Set, Optional, Tuple
import logging
from datetime import datetime, timedelta


class SmartSymbolValidator:
    """智能符號驗證器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
        # 緩存驗證結果（避免重複 API 調用）
        self.validation_cache = {}
        self.cache_expiry = {}
        self.cache_duration = 24 * 3600  # 24小時緩存
        
        # 黑名單：常見的非金融詞彙
        self.blacklist = {
            'PRICE', 'VALUE', 'STOCK', 'MONEY', 'CASH', 'FUND', 'BANK', 'LOAN',
            'DEBT', 'COST', 'FEES', 'RATE', 'YIELD', 'GAIN', 'LOSS', 'PROFIT',
            'TRADE', 'MARKET', 'INDEX', 'CHART', 'GRAPH', 'DATA', 'NEWS',
            'REPORT', 'ANALYSIS', 'TREND', 'BULL', 'BEAR', 'LONG', 'SHORT',
            'CALL', 'PUT', 'OPTION', 'FUTURE', 'SWAP', 'BOND', 'NOTE',
            'SHARE', 'EQUITY', 'ASSET', 'PORTFOLIO', 'ACCOUNT', 'BALANCE',
            'INCOME', 'REVENUE', 'SALES', 'GROWTH', 'VOLUME', 'FLOAT',
            'SPLIT', 'MERGE', 'IPO', 'ICO', 'FORK', 'BURN', 'MINT',
            'WALLET', 'ADDRESS', 'HASH', 'BLOCK', 'CHAIN', 'NODE',
            'MINING', 'STAKING', 'YIELD', 'FARM', 'POOL', 'SWAP',
            'DEFI', 'CEFI', 'DAPP', 'DAO', 'NFT', 'METAVERSE',
            'ABOUT', 'ABOVE', 'AFTER', 'AGAIN', 'AGAINST', 'ALONG',
            'AMONG', 'AROUND', 'BEFORE', 'BELOW', 'BETWEEN', 'BEYOND',
            'DURING', 'EXCEPT', 'INSIDE', 'OUTSIDE', 'THROUGH', 'UNDER',
            'WITHIN', 'WITHOUT', 'ACROSS', 'BEHIND', 'BESIDE', 'TOWARD',
            'SINCE', 'UNTIL', 'WHILE', 'WHERE', 'WHICH', 'WHOSE',
            'THEIR', 'THERE', 'THESE', 'THOSE', 'WOULD', 'COULD',
            'SHOULD', 'MIGHT', 'OUGHT', 'SHALL', 'WILL', 'CAN',
            'HAVE', 'BEEN', 'BEING', 'WERE', 'WAS', 'ARE', 'IS',
            'THE', 'AND', 'OR', 'BUT', 'SO', 'YET', 'FOR', 'NOR',
            'WHAT', 'WHEN', 'WHY', 'HOW', 'WHO', 'WHOM', 'WHOSE',
            'THIS', 'THAT', 'THEY', 'THEM', 'HE', 'SHE', 'IT',
            'WE', 'YOU', 'I', 'ME', 'MY', 'OUR', 'YOUR', 'HIS', 'HER'
        }
        
        # 金融上下文關鍵詞
        self.financial_keywords = {
            'stock': ['stock', 'share', 'ticker', 'equity', 'company', 'corp', 'inc', 'ltd'],
            'crypto': ['crypto', 'coin', 'token', 'blockchain', 'wallet', 'exchange', 'mining', 'defi'],
            'trading': ['buy', 'sell', 'trade', 'hold', 'pump', 'dump', 'moon', 'rocket', 'diamond', 'hands'],
            'financial': ['price', 'value', 'market', 'cap', 'volume', 'earnings', 'revenue', 'profit', 'loss']
        }
        
        # API 配置
        self.api_configs = {
            'alpha_vantage': {
                'base_url': 'https://www.alphavantage.co/query',
                'api_key': 'demo',  # 使用演示密鑰，實際使用時需要註冊
                'rate_limit': 5  # 每分鐘5次調用
            },
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'rate_limit': 50  # 每分鐘50次調用
            }
        }
        
        # API 調用計數器
        self.api_calls = {}
        self.last_api_reset = {}
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('SmartSymbolValidator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def validate_symbol(self, symbol: str, context: str = "", entity_type: str = "unknown") -> Tuple[bool, float, str]:
        """
        驗證符號是否為真實的股票或加密貨幣
        
        Returns:
            (is_valid, confidence, reason)
        """
        symbol = symbol.upper().strip()
        
        # 檢查緩存
        cache_key = f"{symbol}_{entity_type}"
        if self._is_cached_valid(cache_key):
            cached_result = self.validation_cache[cache_key]
            return cached_result['is_valid'], cached_result['confidence'], cached_result['reason']
        
        # 多層驗證
        validations = []
        
        # 第一層：基本格式檢查
        format_valid, format_confidence, format_reason = self._validate_format(symbol)
        validations.append((format_valid, format_confidence, format_reason))
        
        if not format_valid:
            result = (False, format_confidence, format_reason)
            self._cache_result(cache_key, result)
            return result
        
        # 第二層：黑名單檢查
        blacklist_valid, blacklist_confidence, blacklist_reason = self._validate_blacklist(symbol)
        validations.append((blacklist_valid, blacklist_confidence, blacklist_reason))
        
        if not blacklist_valid:
            result = (False, blacklist_confidence, blacklist_reason)
            self._cache_result(cache_key, result)
            return result
        
        # 第三層：上下文分析
        context_valid, context_confidence, context_reason = self._validate_context(symbol, context, entity_type)
        validations.append((context_valid, context_confidence, context_reason))
        
        # 第四層：API 驗證（如果前面的驗證通過）
        if context_confidence > 0.3:
            api_valid, api_confidence, api_reason = self._validate_with_api(symbol, entity_type)
            validations.append((api_valid, api_confidence, api_reason))
        
        # 綜合評分
        final_valid, final_confidence, final_reason = self._combine_validations(validations)
        
        result = (final_valid, final_confidence, final_reason)
        self._cache_result(cache_key, result)
        
        return result
    
    def _validate_format(self, symbol: str) -> Tuple[bool, float, str]:
        """驗證符號格式"""
        # 基本格式：1-10個字母或數字
        if not re.match(r'^[A-Z0-9]{1,10}$', symbol):
            return False, 0.0, "Invalid format"
        
        # 不能全是數字
        if symbol.isdigit():
            return False, 0.0, "All digits"
        
        # 長度檢查
        if len(symbol) < 1 or len(symbol) > 10:
            return False, 0.0, "Invalid length"
        
        # 常見股票長度 1-5，加密貨幣 2-10
        if 1 <= len(symbol) <= 5:
            return True, 0.8, "Stock-like format"
        elif 2 <= len(symbol) <= 10:
            return True, 0.6, "Crypto-like format"
        else:
            return True, 0.4, "Unusual format"
    
    def _validate_blacklist(self, symbol: str) -> Tuple[bool, float, str]:
        """檢查黑名單"""
        if symbol in self.blacklist:
            return False, 0.9, f"In blacklist: {symbol}"
        
        # 檢查是否為常見英文單詞模式
        common_word_patterns = [
            r'^(THE|AND|FOR|ARE|BUT|NOT|YOU|ALL|CAN|HER|WAS|ONE|OUR|OUT|DAY|GET|HAS|HIM|HIS|HOW|ITS|MAY|NEW|NOW|OLD|SEE|TWO|WHO|BOY|DID|ITS|LET|MAN|NEW|NOW|OLD|PUT|SAY|SHE|TOO|USE)$',
            r'^(ABOUT|AFTER|AGAIN|COULD|EVERY|FIRST|FOUND|GREAT|GROUP|HOUSE|LARGE|LAST|LEAVE|LIFE|LITTLE|LONG|MADE|MAKE|MANY|MIGHT|MOVE|MUCH|NAME|NEED|NEVER|NEXT|NIGHT|NUMBER|OFTEN|ORDER|OTHER|OVER|OWN|PART|PLACE|POINT|RIGHT|SAID|SAME|SEEM|SEVERAL|SHALL|SHOW|SINCE|SMALL|SOME|STILL|SUCH|TAKE|THAN|THAT|THEIR|THEM|THESE|THEY|THING|THINK|THIS|THOSE|THOUGH|THREE|THROUGH|TIME|TURN|UNDER|UNTIL|VERY|WANT|WATER|WAYS|WELL|WENT|WERE|WHAT|WHERE|WHICH|WHILE|WHO|WILL|WITH|WORK|WOULD|WRITE|YEAR|YOUNG)$'
        ]
        
        for pattern in common_word_patterns:
            if re.match(pattern, symbol):
                return False, 0.8, f"Common English word: {symbol}"
        
        return True, 0.9, "Not in blacklist"
    
    def _validate_context(self, symbol: str, context: str, entity_type: str) -> Tuple[bool, float, str]:
        """分析上下文"""
        if not context:
            return True, 0.5, "No context provided"
        
        context_lower = context.lower()
        
        # 計算金融關鍵詞出現次數
        financial_score = 0
        found_keywords = []
        
        for category, keywords in self.financial_keywords.items():
            for keyword in keywords:
                if keyword in context_lower:
                    financial_score += 1
                    found_keywords.append(keyword)
        
        # 檢查符號周圍的上下文
        symbol_pattern = rf'\b{re.escape(symbol)}\b'
        matches = list(re.finditer(symbol_pattern, context, re.IGNORECASE))
        
        context_clues = 0
        for match in matches:
            start, end = match.span()
            # 檢查前後20個字符
            before = context[max(0, start-20):start].lower()
            after = context[end:end+20].lower()
            
            # 股票相關線索
            stock_clues = ['$', 'ticker', 'stock', 'share', 'buy', 'sell', 'hold', 'price', 'earnings']
            crypto_clues = ['coin', 'token', 'crypto', 'blockchain', 'wallet', 'exchange']
            
            for clue in stock_clues:
                if clue in before or clue in after:
                    context_clues += 1
                    if entity_type == "stock":
                        context_clues += 1
            
            for clue in crypto_clues:
                if clue in before or clue in after:
                    context_clues += 1
                    if entity_type == "crypto":
                        context_clues += 1
        
        # 計算信心度
        total_score = financial_score + context_clues
        if total_score >= 3:
            confidence = min(0.9, 0.5 + total_score * 0.1)
            return True, confidence, f"Strong financial context (score: {total_score})"
        elif total_score >= 1:
            confidence = 0.3 + total_score * 0.1
            return True, confidence, f"Some financial context (score: {total_score})"
        else:
            return True, 0.2, "No clear financial context"
    
    def _validate_with_api(self, symbol: str, entity_type: str) -> Tuple[bool, float, str]:
        """使用 API 驗證符號"""
        try:
            if entity_type == "crypto":
                return self._validate_crypto_api(symbol)
            else:
                return self._validate_stock_api(symbol)
        except Exception as e:
            self.logger.warning(f"API validation failed for {symbol}: {e}")
            return True, 0.5, "API validation failed"
    
    def _validate_stock_api(self, symbol: str) -> Tuple[bool, float, str]:
        """使用股票 API 驗證"""
        # 檢查 API 調用限制
        if not self._can_make_api_call('alpha_vantage'):
            return True, 0.5, "API rate limit reached"
        
        try:
            # 使用 Alpha Vantage API（演示版本）
            url = f"{self.api_configs['alpha_vantage']['base_url']}"
            params = {
                'function': 'GLOBAL_QUOTE',
                'symbol': symbol,
                'apikey': self.api_configs['alpha_vantage']['api_key']
            }
            
            response = requests.get(url, params=params, timeout=10)
            self._record_api_call('alpha_vantage')
            
            if response.status_code == 200:
                data = response.json()
                
                # 檢查是否有有效數據
                if 'Global Quote' in data and data['Global Quote']:
                    return True, 0.9, "Confirmed by stock API"
                elif 'Error Message' in data:
                    return False, 0.8, "Not found in stock API"
                else:
                    return True, 0.3, "Unclear stock API response"
            else:
                return True, 0.5, "Stock API request failed"
                
        except Exception as e:
            self.logger.warning(f"Stock API validation error: {e}")
            return True, 0.5, "Stock API error"
    
    def _validate_crypto_api(self, symbol: str) -> Tuple[bool, float, str]:
        """使用加密貨幣 API 驗證"""
        # 檢查 API 調用限制
        if not self._can_make_api_call('coingecko'):
            return True, 0.5, "API rate limit reached"
        
        try:
            # 使用 CoinGecko API
            url = f"{self.api_configs['coingecko']['base_url']}/coins/list"
            
            response = requests.get(url, timeout=10)
            self._record_api_call('coingecko')
            
            if response.status_code == 200:
                coins = response.json()
                
                # 檢查符號是否存在
                for coin in coins:
                    if coin.get('symbol', '').upper() == symbol:
                        return True, 0.9, f"Confirmed by crypto API: {coin.get('name', 'Unknown')}"
                
                return False, 0.8, "Not found in crypto API"
            else:
                return True, 0.5, "Crypto API request failed"
                
        except Exception as e:
            self.logger.warning(f"Crypto API validation error: {e}")
            return True, 0.5, "Crypto API error"
    
    def _combine_validations(self, validations: List[Tuple[bool, float, str]]) -> Tuple[bool, float, str]:
        """綜合多個驗證結果"""
        if not validations:
            return False, 0.0, "No validations"
        
        # 如果任何驗證明確拒絕，則拒絕
        for valid, confidence, reason in validations:
            if not valid and confidence > 0.7:
                return False, confidence, reason
        
        # 計算加權平均信心度
        total_weight = 0
        weighted_confidence = 0
        reasons = []
        
        for valid, confidence, reason in validations:
            if valid:
                weight = confidence
                total_weight += weight
                weighted_confidence += confidence * weight
                reasons.append(reason)
        
        if total_weight == 0:
            return False, 0.0, "All validations failed"
        
        final_confidence = weighted_confidence / total_weight
        final_valid = final_confidence > 0.5
        
        return final_valid, final_confidence, "; ".join(reasons)
    
    def _can_make_api_call(self, api_name: str) -> bool:
        """檢查是否可以進行 API 調用"""
        now = time.time()
        
        # 重置計數器（每分鐘）
        if api_name not in self.last_api_reset or now - self.last_api_reset[api_name] > 60:
            self.api_calls[api_name] = 0
            self.last_api_reset[api_name] = now
        
        # 檢查限制
        limit = self.api_configs[api_name]['rate_limit']
        return self.api_calls.get(api_name, 0) < limit
    
    def _record_api_call(self, api_name: str):
        """記錄 API 調用"""
        self.api_calls[api_name] = self.api_calls.get(api_name, 0) + 1
    
    def _is_cached_valid(self, cache_key: str) -> bool:
        """檢查緩存是否有效"""
        if cache_key not in self.validation_cache:
            return False
        
        if cache_key not in self.cache_expiry:
            return False
        
        return time.time() < self.cache_expiry[cache_key]
    
    def _cache_result(self, cache_key: str, result: Tuple[bool, float, str]):
        """緩存驗證結果"""
        self.validation_cache[cache_key] = {
            'is_valid': result[0],
            'confidence': result[1],
            'reason': result[2]
        }
        self.cache_expiry[cache_key] = time.time() + self.cache_duration
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """獲取緩存統計"""
        return {
            'cache_size': len(self.validation_cache),
            'api_calls': self.api_calls.copy(),
            'last_reset': self.last_api_reset.copy()
        }
