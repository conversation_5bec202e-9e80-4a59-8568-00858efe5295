"""
配置管理模組
管理環境變量和應用配置
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any

# 加載環境變量
load_dotenv()


class Config:
    """應用配置類"""
    
    # Reddit API 配置
    REDDIT_CLIENT_ID = os.getenv('REDDIT_CLIENT_ID')
    REDDIT_CLIENT_SECRET = os.getenv('REDDIT_CLIENT_SECRET')
    REDDIT_USER_AGENT = os.getenv('REDDIT_USER_AGENT')
    REDDIT_USERNAME = os.getenv('REDDIT_USERNAME')
    REDDIT_PASSWORD = os.getenv('REDDIT_PASSWORD')
    
    # OpenRouter API 配置
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # 數據庫配置
    DATABASE_PATH = "data/reddit_monitor.db"
    
    # 數據收集配置
    POSTS_PER_SUBREDDIT = 100  # 每個 subreddit 收集的帖子數量
    COMMENTS_PER_POST = 50     # 每個帖子收集的評論數量
    COLLECTION_INTERVAL = 3600  # 收集間隔（秒）

    # 大量數據收集配置
    BULK_POSTS_PER_SUBREDDIT = 200  # 批量收集時每個 subreddit 的帖子數量
    BULK_COMMENTS_PER_POST = 100    # 批量收集時每個帖子的評論數量
    TARGET_TOTAL_POSTS = 1000       # 目標總帖子數量
    MAX_SUBREDDITS_FOR_BULK = 10    # 批量收集時的最大 subreddit 數量
    
    # 情緒分析配置
    SENTIMENT_MODEL = "anthropic/claude-3-haiku"  # 使用的 LLM 模型
    SENTIMENT_BATCH_SIZE = 10  # 批處理大小
    
    # 報告配置
    REPORT_INTERVAL = 3600  # 報告生成間隔（秒）
    REPORTS_DIR = "reports"
    
    # 日誌配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "app.log"
    
    @classmethod
    def validate(cls) -> bool:
        """驗證配置是否完整"""
        required_vars = [
            'REDDIT_CLIENT_ID',
            'REDDIT_CLIENT_SECRET', 
            'REDDIT_USER_AGENT',
            'REDDIT_USERNAME',
            'REDDIT_PASSWORD',
            'OPENROUTER_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ 缺少必要的環境變量: {', '.join(missing_vars)}")
            return False
        
        print("✅ 配置驗證通過")
        return True
    
    @classmethod
    def get_reddit_config(cls) -> Dict[str, str]:
        """獲取 Reddit API 配置"""
        return {
            'client_id': cls.REDDIT_CLIENT_ID,
            'client_secret': cls.REDDIT_CLIENT_SECRET,
            'user_agent': cls.REDDIT_USER_AGENT,
            'username': cls.REDDIT_USERNAME,
            'password': cls.REDDIT_PASSWORD
        }
    
    @classmethod
    def get_openrouter_config(cls) -> Dict[str, str]:
        """獲取 OpenRouter API 配置"""
        return {
            'api_key': cls.OPENROUTER_API_KEY,
            'base_url': cls.OPENROUTER_BASE_URL,
            'model': cls.SENTIMENT_MODEL
        }


# 股票和加密貨幣符號配置
STOCK_SYMBOLS = {
    # 主要股票
    'AAPL': 'Apple Inc.',
    'MSFT': 'Microsoft Corporation',
    'GOOGL': 'Alphabet Inc.',
    'GOOG': 'Alphabet Inc. Class C',
    'AMZN': 'Amazon.com Inc.',
    'TSLA': 'Tesla Inc.',
    'META': 'Meta Platforms Inc.',
    'NVDA': 'NVIDIA Corporation',
    'NFLX': 'Netflix Inc.',
    'AMD': 'Advanced Micro Devices',
    'INTC': 'Intel Corporation',
    'CRM': 'Salesforce Inc.',
    'ORCL': 'Oracle Corporation',
    'ADBE': 'Adobe Inc.',
    'PYPL': 'PayPal Holdings Inc.',
    'UBER': 'Uber Technologies Inc.',
    'SPOT': 'Spotify Technology S.A.',
    'SQ': 'Block Inc.',
    'ROKU': 'Roku Inc.',
    'ZM': 'Zoom Video Communications',
    'SHOP': 'Shopify Inc.',
    'BABA': 'Alibaba Group Holding',
    'JD': 'JD.com Inc.',
    'PDD': 'PDD Holdings Inc.',
    'COIN': 'Coinbase Global Inc.',
    'HOOD': 'Robinhood Markets Inc.',
    'SOFI': 'SoFi Technologies Inc.',
    'UPST': 'Upstart Holdings Inc.',
    'AFRM': 'Affirm Holdings Inc.',
    'RBLX': 'Roblox Corporation',
    'U': 'Unity Software Inc.',
    'SNOW': 'Snowflake Inc.',
    'CRWD': 'CrowdStrike Holdings Inc.',
    'ZS': 'Zscaler Inc.',
    'OKTA': 'Okta Inc.',
    'DDOG': 'Datadog Inc.',
    'MDB': 'MongoDB Inc.',
    'TEAM': 'Atlassian Corporation',
    'WDAY': 'Workday Inc.',
    'VEEV': 'Veeva Systems Inc.',
    'DOCU': 'DocuSign Inc.',
    'TWLO': 'Twilio Inc.',
    'NET': 'Cloudflare Inc.',
    'FSLY': 'Fastly Inc.',
    'ESTC': 'Elastic N.V.',

    # 熱門 meme 股票
    'GME': 'GameStop Corp.',
    'AMC': 'AMC Entertainment Holdings',
    'BB': 'BlackBerry Limited',
    'NOK': 'Nokia Corporation',
    'PLTR': 'Palantir Technologies Inc.',
    'WISH': 'ContextLogic Inc.',
    'CLOV': 'Clover Health Investments',
    'SPCE': 'Virgin Galactic Holdings',
    'BBBY': 'Bed Bath & Beyond Inc.',
    'EXPR': 'Express Inc.',
    'KOSS': 'Koss Corporation',
    'NAKD': 'Naked Brand Group',
    'SNDL': 'Sundial Growers Inc.',
    'TLRY': 'Tilray Brands Inc.',
    'CGC': 'Canopy Growth Corporation',
    'ACB': 'Aurora Cannabis Inc.',
    'HEXO': 'HEXO Corp.',
    'APHA': 'Aphria Inc.',
    'CRON': 'Cronos Group Inc.',
    'OGI': 'OrganiGram Holdings Inc.',

    # 小市值和新興股票
    'RIVN': 'Rivian Automotive Inc.',
    'LCID': 'Lucid Group Inc.',
    'NIO': 'NIO Inc.',
    'XPEV': 'XPeng Inc.',
    'LI': 'Li Auto Inc.',
    'NKLA': 'Nikola Corporation',
    'RIDE': 'Lordstown Motors Corp.',
    'GOEV': 'Canoo Inc.',
    'HYLN': 'Hyliion Holdings Corp.',
    'WKHS': 'Workhorse Group Inc.',
    'BLNK': 'Blink Charging Co.',
    'CHPT': 'ChargePoint Holdings Inc.',
    'EVGO': 'EVgo Inc.',
    'STEM': 'Stem Inc.',
    'ENPH': 'Enphase Energy Inc.',
    'SEDG': 'SolarEdge Technologies Inc.',
    'FSLR': 'First Solar Inc.',
    'SPWR': 'SunPower Corporation',
    'RUN': 'Sunrun Inc.',
    'NOVA': 'Sunnova Energy International',

    # ETFs
    'SPY': 'SPDR S&P 500 ETF Trust',
    'QQQ': 'Invesco QQQ Trust',
    'VTI': 'Vanguard Total Stock Market ETF',
    'VOO': 'Vanguard S&P 500 ETF',
    'IWM': 'iShares Russell 2000 ETF',
    'VXX': 'iPath Series B S&P 500 VIX',
    'SQQQ': 'ProShares UltraPro Short QQQ',
    'TQQQ': 'ProShares UltraPro QQQ',
    'SPXL': 'Direxion Daily S&P 500 Bull 3X',
    'SPXS': 'Direxion Daily S&P 500 Bear 3X'
}

CRYPTO_SYMBOLS = {
    # 主要加密貨幣
    'BTC': 'Bitcoin',
    'ETH': 'Ethereum',
    'BNB': 'Binance Coin',
    'XRP': 'Ripple',
    'ADA': 'Cardano',
    'SOL': 'Solana',
    'DOGE': 'Dogecoin',
    'DOT': 'Polkadot',
    'AVAX': 'Avalanche',
    'SHIB': 'Shiba Inu',
    'MATIC': 'Polygon',
    'LTC': 'Litecoin',
    'UNI': 'Uniswap',
    'LINK': 'Chainlink',
    'ATOM': 'Cosmos',
    'XLM': 'Stellar',
    'VET': 'VeChain',
    'ICP': 'Internet Computer',
    'FIL': 'Filecoin',
    'TRX': 'TRON',
    
    # DeFi 代幣
    'AAVE': 'Aave',
    'COMP': 'Compound',
    'MKR': 'Maker',
    'SNX': 'Synthetix',
    'YFI': 'yearn.finance',
    'SUSHI': 'SushiSwap',
    'CRV': 'Curve DAO Token',
    '1INCH': '1inch',
    
    # Meme 幣
    'PEPE': 'Pepe',
    'FLOKI': 'Floki Inu',
    'BABYDOGE': 'Baby Doge Coin'
}

# 情緒分析提示詞模板
SENTIMENT_PROMPTS = {
    'analysis': """
請分析以下 Reddit 帖子/評論的情緒，特別關注對股票或加密貨幣的態度：

內容: "{content}"

請提供：
1. 情緒分數（-1.0 到 1.0，-1.0 = 非常負面，0 = 中性，1.0 = 非常正面）
2. 情緒標籤（positive/negative/neutral）
3. 信心度（0.0 到 1.0）
4. 簡短解釋

請以 JSON 格式回應：
{{
    "sentiment_score": 0.0,
    "sentiment_label": "neutral",
    "confidence": 0.0,
    "explanation": "簡短解釋"
}}
""",
    
    'entity_extraction': """
請從以下 Reddit 內容中提取所有提及的股票代碼和加密貨幣符號：

內容: "{content}"

請識別：
1. 股票代碼（如 AAPL, TSLA, GME 等）
2. 加密貨幣符號（如 BTC, ETH, DOGE 等）
3. 提及的上下文

請以 JSON 格式回應：
{{
    "entities": [
        {{
            "type": "stock",
            "symbol": "AAPL",
            "name": "Apple Inc.",
            "context": "提及的上下文",
            "confidence": 0.9
        }}
    ]
}}
"""
}
