"""
報告生成器
生成 HTML 和 JSON 格式的市場情緒報告
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging
from jinja2 import Environment, FileSystemLoader

from ..analyzers.trend_analyzer import TrendAnalyzer
from ..storage.database import DatabaseManager
from ..utils.config import Config


class ReportGenerator:
    """報告生成器"""
    
    def __init__(self):
        self.config = Config()
        self.trend_analyzer = TrendAnalyzer()
        self.db = DatabaseManager()
        self.logger = self._setup_logger()
        
        # 確保報告目錄存在
        os.makedirs(self.config.REPORTS_DIR, exist_ok=True)
        
        # 設置 Jinja2 模板環境
        template_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.jinja_env = Environment(loader=FileSystemLoader(template_dir))
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('ReportGenerator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_hourly_report(self) -> str:
        """生成每小時報告"""
        try:
            # 收集數據 - 使用6小時窗口確保有足夠數據
            report_data = self._collect_report_data("6h")
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            
            # 生成 JSON 報告
            json_path = os.path.join(self.config.REPORTS_DIR, f"hourly_report_{timestamp}.json")
            self._generate_json_report(report_data, json_path)
            
            # 生成 HTML 報告
            html_path = os.path.join(self.config.REPORTS_DIR, f"hourly_report_{timestamp}.html")
            self._generate_html_report(report_data, html_path, "hourly")
            
            # 生成最新報告的符號鏈接
            self._create_latest_links(json_path, html_path, "hourly")
            
            # 保存報告記錄到數據庫
            self._save_report_record("hourly", html_path, report_data)
            
            self.logger.info(f"✅ 每小時報告生成完成: {html_path}")
            return html_path
            
        except Exception as e:
            self.logger.error(f"❌ 每小時報告生成失敗: {e}")
            raise
    
    def generate_daily_report(self) -> str:
        """生成每日報告"""
        try:
            # 收集數據
            report_data = self._collect_report_data("24h")
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d")
            
            # 生成 JSON 報告
            json_path = os.path.join(self.config.REPORTS_DIR, f"daily_report_{timestamp}.json")
            self._generate_json_report(report_data, json_path)
            
            # 生成 HTML 報告
            html_path = os.path.join(self.config.REPORTS_DIR, f"daily_report_{timestamp}.html")
            self._generate_html_report(report_data, html_path, "daily")
            
            # 生成最新報告的符號鏈接
            self._create_latest_links(json_path, html_path, "daily")
            
            # 保存報告記錄到數據庫
            self._save_report_record("daily", html_path, report_data)
            
            self.logger.info(f"✅ 每日報告生成完成: {html_path}")
            return html_path
            
        except Exception as e:
            self.logger.error(f"❌ 每日報告生成失敗: {e}")
            raise
    
    def generate_weekly_report(self) -> str:
        """生成每週報告"""
        try:
            # 收集數據
            report_data = self._collect_report_data("7d")
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y_W%U")
            
            # 生成 JSON 報告
            json_path = os.path.join(self.config.REPORTS_DIR, f"weekly_report_{timestamp}.json")
            self._generate_json_report(report_data, json_path)
            
            # 生成 HTML 報告
            html_path = os.path.join(self.config.REPORTS_DIR, f"weekly_report_{timestamp}.html")
            self._generate_html_report(report_data, html_path, "weekly")
            
            # 生成最新報告的符號鏈接
            self._create_latest_links(json_path, html_path, "weekly")
            
            # 保存報告記錄到數據庫
            self._save_report_record("weekly", html_path, report_data)
            
            self.logger.info(f"✅ 每週報告生成完成: {html_path}")
            return html_path
            
        except Exception as e:
            self.logger.error(f"❌ 每週報告生成失敗: {e}")
            raise
    
    def _collect_report_data(self, time_period: str) -> Dict[str, Any]:
        """收集報告數據"""
        # 獲取市場概覽
        market_overview = self.trend_analyzer.get_market_overview(time_period)
        
        # 獲取各種排名的股票和加密貨幣
        top_stocks = self.trend_analyzer.get_top_trending("stock", time_period, 15)
        top_cryptos = self.trend_analyzer.get_top_trending("crypto", time_period, 15)

        # 獲取特殊排名
        momentum_stocks = self.trend_analyzer.get_trending_by_momentum("stock", time_period, 10)
        momentum_cryptos = self.trend_analyzer.get_trending_by_momentum("crypto", time_period, 10)

        niche_stocks = self.trend_analyzer.get_niche_favorites("stock", time_period, 10)
        niche_cryptos = self.trend_analyzer.get_niche_favorites("crypto", time_period, 10)

        meme_stocks = self.trend_analyzer.get_meme_potential("stock", time_period, 10)
        meme_cryptos = self.trend_analyzer.get_meme_potential("crypto", time_period, 10)

        gem_stocks = self.trend_analyzer.get_small_cap_gems("stock", time_period, 10)
        gem_cryptos = self.trend_analyzer.get_small_cap_gems("crypto", time_period, 10)
        
        # 獲取異常趨勢
        anomalies = self.trend_analyzer.detect_anomalies()
        
        # 獲取情緒統計
        hours = self._time_period_to_hours(time_period)
        sentiment_stats = self.db.get_sentiment_summary(hours)
        
        # 獲取活躍的 subreddits
        active_subreddits = self._get_active_subreddits_stats(hours)
        
        return {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'time_period': time_period,
                'report_type': self._get_report_type(time_period)
            },
            'market_overview': market_overview,
            'top_stocks': top_stocks,
            'top_cryptos': top_cryptos,
            'momentum_stocks': momentum_stocks,
            'momentum_cryptos': momentum_cryptos,
            'niche_stocks': niche_stocks,
            'niche_cryptos': niche_cryptos,
            'meme_stocks': meme_stocks,
            'meme_cryptos': meme_cryptos,
            'gem_stocks': gem_stocks,
            'gem_cryptos': gem_cryptos,
            'anomalies': anomalies,
            'sentiment_stats': sentiment_stats,
            'active_subreddits': active_subreddits
        }
    
    def _generate_json_report(self, data: Dict[str, Any], file_path: str):
        """生成 JSON 報告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    def _generate_html_report(self, data: Dict[str, Any], file_path: str, report_type: str):
        """生成 HTML 報告"""
        try:
            template = self.jinja_env.get_template('report_template.html')
            
            # 準備模板數據
            template_data = {
                **data,
                'report_title': self._get_report_title(report_type),
                'css_styles': self._get_css_styles(),
                'javascript': self._get_javascript()
            }
            
            # 渲染模板
            html_content = template.render(**template_data)
            
            # 寫入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
        except Exception as e:
            self.logger.error(f"HTML 報告生成失敗: {e}")
            # 生成簡單的 HTML 報告作為備用
            self._generate_simple_html_report(data, file_path, report_type)
    
    def _generate_simple_html_report(self, data: Dict[str, Any], file_path: str, report_type: str):
        """生成簡單的 HTML 報告（備用方案）"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{self._get_report_title(report_type)}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .trend-item {{ background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
                .neutral {{ color: gray; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self._get_report_title(report_type)}</h1>
                <p>生成時間: {data['metadata']['generated_at']}</p>
                <p>時間範圍: {data['metadata']['time_period']}</p>
            </div>
            
            <div class="section">
                <h2>📈 熱門股票</h2>
                {self._format_trends_html(data['top_stocks'])}
            </div>
            
            <div class="section">
                <h2>💰 熱門加密貨幣</h2>
                {self._format_trends_html(data['top_cryptos'])}
            </div>
            
            <div class="section">
                <h2>🎭 市場情緒</h2>
                <p>股票平均情緒: {data['market_overview']['market_sentiment']['stocks']['avg_sentiment']:.2f}</p>
                <p>加密貨幣平均情緒: {data['market_overview']['market_sentiment']['crypto']['avg_sentiment']:.2f}</p>
            </div>
            
            <div class="section">
                <h2>📊 活動統計</h2>
                <p>收集帖子: {data['market_overview']['activity_stats']['posts_collected']}</p>
                <p>收集評論: {data['market_overview']['activity_stats']['comments_collected']}</p>
                <p>分析情緒: {data['market_overview']['activity_stats']['sentiments_analyzed']}</p>
                <p>提取實體: {data['market_overview']['activity_stats']['entities_extracted']}</p>
            </div>
        </body>
        </html>
        """
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _format_trends_html(self, trends: List[Dict[str, Any]]) -> str:
        """格式化趨勢數據為 HTML"""
        if not trends:
            return "<p>暫無數據</p>"
        
        html = ""
        for trend in trends[:10]:  # 只顯示前10個
            sentiment_class = self._get_sentiment_class(trend.get('avg_sentiment_score', 0))
            html += f"""
            <div class="trend-item">
                <strong>{trend['symbol']}</strong> - 
                提及次數: {trend['mention_count']}, 
                趨勢分數: {trend['trend_score']:.2f}
                <span class="{sentiment_class}">
                    (情緒: {trend.get('avg_sentiment_score', 0):.2f})
                </span>
            </div>
            """
        return html
    
    def _get_sentiment_class(self, sentiment_score: float) -> str:
        """獲取情緒對應的 CSS 類"""
        if sentiment_score > 0.1:
            return "positive"
        elif sentiment_score < -0.1:
            return "negative"
        else:
            return "neutral"
    
    def _create_latest_links(self, json_path: str, html_path: str, report_type: str):
        """創建最新報告的符號鏈接"""
        try:
            latest_json = os.path.join(self.config.REPORTS_DIR, f"latest_{report_type}.json")
            latest_html = os.path.join(self.config.REPORTS_DIR, f"latest_{report_type}.html")
            
            # 刪除舊的符號鏈接
            for path in [latest_json, latest_html]:
                if os.path.exists(path):
                    os.remove(path)
            
            # 創建新的符號鏈接
            os.symlink(os.path.basename(json_path), latest_json)
            os.symlink(os.path.basename(html_path), latest_html)
            
        except Exception as e:
            self.logger.warning(f"創建符號鏈接失敗: {e}")
    
    def _save_report_record(self, report_type: str, file_path: str, data: Dict[str, Any]):
        """保存報告記錄到數據庫"""
        try:
            conn = self.db.models.get_connection()
            cursor = conn.cursor()
            
            # 準備摘要數據
            summary = f"Generated {report_type} report with {len(data['top_stocks'])} stocks and {len(data['top_cryptos'])} cryptos"
            top_stocks_json = json.dumps(data['top_stocks'][:5])  # 只保存前5個
            top_cryptos_json = json.dumps(data['top_cryptos'][:5])  # 只保存前5個
            
            cursor.execute('''
                INSERT INTO reports (report_type, file_path, summary, top_stocks, top_cryptos, generated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (report_type, file_path, summary, top_stocks_json, top_cryptos_json, datetime.now()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"保存報告記錄失敗: {e}")
    
    def _get_active_subreddits_stats(self, hours: int) -> List[Dict[str, Any]]:
        """獲取活躍 subreddits 統計"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.name, s.display_name, s.category, COUNT(p.id) as post_count
            FROM subreddits s
            LEFT JOIN posts p ON s.id = p.subreddit_id 
                AND p.created_utc >= datetime('now', '-{} hours')
            WHERE s.is_active = 1
            GROUP BY s.id, s.name, s.display_name, s.category
            ORDER BY post_count DESC
            LIMIT 20
        '''.format(hours))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def _time_period_to_hours(self, time_period: str) -> int:
        """將時間段轉換為小時數"""
        if time_period == "1h":
            return 1
        elif time_period == "6h":
            return 6
        elif time_period == "24h":
            return 24
        elif time_period == "7d":
            return 168
        else:
            return 24
    
    def _get_report_type(self, time_period: str) -> str:
        """根據時間段獲取報告類型"""
        if time_period == "1h":
            return "hourly"
        elif time_period == "24h":
            return "daily"
        elif time_period == "7d":
            return "weekly"
        else:
            return "custom"
    
    def _get_report_title(self, report_type: str) -> str:
        """獲取報告標題"""
        titles = {
            "hourly": "Reddit 股票加密貨幣情緒監控 - 每小時報告",
            "daily": "Reddit 股票加密貨幣情緒監控 - 每日報告",
            "weekly": "Reddit 股票加密貨幣情緒監控 - 每週報告"
        }
        return titles.get(report_type, "Reddit 股票加密貨幣情緒監控報告")
    
    def _get_css_styles(self) -> str:
        """獲取 CSS 樣式"""
        return """
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin: 30px 0; }
        .trend-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .trend-card { background: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        .metric { display: inline-block; margin: 5px 10px; padding: 5px 10px; background: #e9ecef; border-radius: 15px; font-size: 0.9em; }
        """
    
    def _get_javascript(self) -> str:
        """獲取 JavaScript 代碼"""
        return """
        function updateTimestamp() {
            const now = new Date();
            const timestamp = document.getElementById('current-time');
            if (timestamp) {
                timestamp.textContent = now.toLocaleString();
            }
        }
        setInterval(updateTimestamp, 1000);
        updateTimestamp();
        """
