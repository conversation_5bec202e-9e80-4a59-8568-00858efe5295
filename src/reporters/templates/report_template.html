<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report_title }}</title>
    <style>
        {{ css_styles }}
        
        /* 額外樣式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .sentiment-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .sentiment-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .anomaly-alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .subreddit-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .subreddit-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
        
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 標題區域 -->
        <div class="header">
            <h1>{{ report_title }}</h1>
            <p class="timestamp">生成時間: {{ metadata.generated_at }}</p>
            <p class="timestamp">時間範圍: {{ metadata.time_period }}</p>
            <p class="timestamp">當前時間: <span id="current-time"></span></p>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新報告</button>
        </div>

        <!-- 統計概覽 -->
        <div class="section">
            <h2>📊 統計概覽</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ market_overview.activity_stats.posts_collected }}</div>
                    <div class="stat-label">收集帖子</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ market_overview.activity_stats.comments_collected }}</div>
                    <div class="stat-label">收集評論</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ market_overview.activity_stats.sentiments_analyzed }}</div>
                    <div class="stat-label">情緒分析</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ market_overview.activity_stats.entities_extracted }}</div>
                    <div class="stat-label">實體提取</div>
                </div>
            </div>
        </div>

        <!-- 市場情緒 -->
        <div class="section">
            <h2>🎭 市場情緒</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>📈 股票市場</h3>
                    <p>平均情緒: <span class="{{ 'positive' if market_overview.market_sentiment.stocks.avg_sentiment > 0.1 else 'negative' if market_overview.market_sentiment.stocks.avg_sentiment < -0.1 else 'neutral' }}">{{ "%.2f"|format(market_overview.market_sentiment.stocks.avg_sentiment) }}</span></p>
                    <p>總提及次數: {{ market_overview.market_sentiment.stocks.total_mentions }}</p>
                    <div class="sentiment-bar">
                        <div class="sentiment-fill" style="width: {{ (market_overview.market_sentiment.stocks.positive_ratio * 100)|round }}%; background: #28a745;"></div>
                    </div>
                    <small>正面情緒比例: {{ "%.1f"|format(market_overview.market_sentiment.stocks.positive_ratio * 100) }}%</small>
                </div>
                <div>
                    <h3>💰 加密貨幣市場</h3>
                    <p>平均情緒: <span class="{{ 'positive' if market_overview.market_sentiment.crypto.avg_sentiment > 0.1 else 'negative' if market_overview.market_sentiment.crypto.avg_sentiment < -0.1 else 'neutral' }}">{{ "%.2f"|format(market_overview.market_sentiment.crypto.avg_sentiment) }}</span></p>
                    <p>總提及次數: {{ market_overview.market_sentiment.crypto.total_mentions }}</p>
                    <div class="sentiment-bar">
                        <div class="sentiment-fill" style="width: {{ (market_overview.market_sentiment.crypto.positive_ratio * 100)|round }}%; background: #28a745;"></div>
                    </div>
                    <small>正面情緒比例: {{ "%.1f"|format(market_overview.market_sentiment.crypto.positive_ratio * 100) }}%</small>
                </div>
            </div>
        </div>

        <!-- 熱門股票 -->
        <div class="section">
            <h2>📈 熱門股票 (按趨勢分數)</h2>
            {% if top_stocks %}
            <div class="trend-grid">
                {% for stock in top_stocks[:8] %}
                <div class="trend-card">
                    <h4>{{ stock.symbol }}</h4>
                    <div class="metric">提及: {{ stock.mention_count }}</div>
                    <div class="metric">趨勢分數: {{ "%.2f"|format(stock.trend_score) }}</div>
                    <div class="metric">情緒: <span class="{{ 'positive' if stock.avg_sentiment_score > 0.1 else 'negative' if stock.avg_sentiment_score < -0.1 else 'neutral' }}">{{ "%.2f"|format(stock.avg_sentiment_score) }}</span></div>
                    <div style="margin-top: 10px;">
                        <small>正面: {{ stock.positive_sentiment_count }} | 負面: {{ stock.negative_sentiment_count }} | 中性: {{ stock.neutral_sentiment_count }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p>暫無股票數據</p>
            {% endif %}
        </div>

        <!-- 熱門加密貨幣 -->
        <div class="section">
            <h2>💰 熱門加密貨幣 (按趨勢分數)</h2>
            {% if top_cryptos %}
            <div class="trend-grid">
                {% for crypto in top_cryptos[:8] %}
                <div class="trend-card">
                    <h4>{{ crypto.symbol }}</h4>
                    <div class="metric">提及: {{ crypto.mention_count }}</div>
                    <div class="metric">趨勢分數: {{ "%.2f"|format(crypto.trend_score) }}</div>
                    <div class="metric">情緒: <span class="{{ 'positive' if crypto.avg_sentiment_score > 0.1 else 'negative' if crypto.avg_sentiment_score < -0.1 else 'neutral' }}">{{ "%.2f"|format(crypto.avg_sentiment_score) }}</span></div>
                    <div style="margin-top: 10px;">
                        <small>正面: {{ crypto.positive_sentiment_count }} | 負面: {{ crypto.negative_sentiment_count }} | 中性: {{ crypto.neutral_sentiment_count }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p>暫無加密貨幣數據</p>
            {% endif %}
        </div>

        <!-- 突然爆紅 -->
        <div class="section">
            <h2>🚀 突然爆紅 (動量排行)</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>📈 股票</h3>
                    {% if momentum_stocks %}
                    <div class="trend-grid">
                        {% for stock in momentum_stocks[:5] %}
                        <div class="trend-card" style="border-left-color: #ff6b6b;">
                            <h4>{{ stock.symbol }}</h4>
                            <div class="metric">當前提及: {{ stock.current_mentions }}</div>
                            <div class="metric">動量比: {{ "%.1f"|format(stock.momentum_ratio) }}x</div>
                            <div class="metric">情緒: <span class="{{ 'positive' if stock.avg_sentiment_score > 0.1 else 'negative' if stock.avg_sentiment_score < -0.1 else 'neutral' }}">{{ "%.2f"|format(stock.avg_sentiment_score) }}</span></div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
                <div>
                    <h3>💰 加密貨幣</h3>
                    {% if momentum_cryptos %}
                    <div class="trend-grid">
                        {% for crypto in momentum_cryptos[:5] %}
                        <div class="trend-card" style="border-left-color: #ff6b6b;">
                            <h4>{{ crypto.symbol }}</h4>
                            <div class="metric">當前提及: {{ crypto.current_mentions }}</div>
                            <div class="metric">動量比: {{ "%.1f"|format(crypto.momentum_ratio) }}x</div>
                            <div class="metric">情緒: <span class="{{ 'positive' if crypto.avg_sentiment_score > 0.1 else 'negative' if crypto.avg_sentiment_score < -0.1 else 'neutral' }}">{{ "%.2f"|format(crypto.avg_sentiment_score) }}</span></div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 小眾熱門 -->
        <div class="section">
            <h2>💎 小眾熱門 (高情緒低提及)</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>📈 股票</h3>
                    {% if niche_stocks %}
                    <div class="trend-grid">
                        {% for stock in niche_stocks[:5] %}
                        <div class="trend-card" style="border-left-color: #4ecdc4;">
                            <h4>{{ stock.symbol }}</h4>
                            <div class="metric">提及: {{ stock.mention_count }}</div>
                            <div class="metric">情緒: <span class="positive">{{ "%.2f"|format(stock.avg_sentiment_score) }}</span></div>
                            <div class="metric">正面比例: {{ "%.0f"|format((stock.positive_sentiment_count / stock.mention_count * 100) if stock.mention_count > 0 else 0) }}%</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
                <div>
                    <h3>💰 加密貨幣</h3>
                    {% if niche_cryptos %}
                    <div class="trend-grid">
                        {% for crypto in niche_cryptos[:5] %}
                        <div class="trend-card" style="border-left-color: #4ecdc4;">
                            <h4>{{ crypto.symbol }}</h4>
                            <div class="metric">提及: {{ crypto.mention_count }}</div>
                            <div class="metric">情緒: <span class="positive">{{ "%.2f"|format(crypto.avg_sentiment_score) }}</span></div>
                            <div class="metric">正面比例: {{ "%.0f"|format((crypto.positive_sentiment_count / crypto.mention_count * 100) if crypto.mention_count > 0 else 0) }}%</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Meme 潛力 -->
        <div class="section">
            <h2>🎭 Meme 潛力股</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>📈 股票</h3>
                    {% if meme_stocks %}
                    <div class="trend-grid">
                        {% for stock in meme_stocks[:5] %}
                        <div class="trend-card" style="border-left-color: #ffa726;">
                            <h4>{{ stock.symbol }}</h4>
                            <div class="metric">提及: {{ stock.mention_count }}</div>
                            <div class="metric">Meme 分數: {{ "%.2f"|format(stock.meme_score) }}</div>
                            <div class="metric">正面比例: {{ "%.0f"|format(stock.positive_ratio * 100) }}%</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
                <div>
                    <h3>💰 加密貨幣</h3>
                    {% if meme_cryptos %}
                    <div class="trend-grid">
                        {% for crypto in meme_cryptos[:5] %}
                        <div class="trend-card" style="border-left-color: #ffa726;">
                            <h4>{{ crypto.symbol }}</h4>
                            <div class="metric">提及: {{ crypto.mention_count }}</div>
                            <div class="metric">Meme 分數: {{ "%.2f"|format(crypto.meme_score) }}</div>
                            <div class="metric">正面比例: {{ "%.0f"|format(crypto.positive_ratio * 100) }}%</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 小市值寶石 -->
        <div class="section">
            <h2>💍 小市值寶石 (潛力項目)</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>📈 股票</h3>
                    {% if gem_stocks %}
                    <div class="trend-grid">
                        {% for stock in gem_stocks[:5] %}
                        <div class="trend-card" style="border-left-color: #9c27b0;">
                            <h4>{{ stock.symbol }}</h4>
                            <div class="metric">提及: {{ stock.mention_count }}</div>
                            <div class="metric">寶石分數: {{ "%.2f"|format(stock.gem_score) }}</div>
                            <div class="metric">情緒: <span class="positive">{{ "%.2f"|format(stock.avg_sentiment_score) }}</span></div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
                <div>
                    <h3>💰 加密貨幣</h3>
                    {% if gem_cryptos %}
                    <div class="trend-grid">
                        {% for crypto in gem_cryptos[:5] %}
                        <div class="trend-card" style="border-left-color: #9c27b0;">
                            <h4>{{ crypto.symbol }}</h4>
                            <div class="metric">提及: {{ crypto.mention_count }}</div>
                            <div class="metric">寶石分數: {{ "%.2f"|format(crypto.gem_score) }}</div>
                            <div class="metric">情緒: <span class="positive">{{ "%.2f"|format(crypto.avg_sentiment_score) }}</span></div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p>暫無數據</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 異常趨勢 -->
        {% if anomalies %}
        <div class="section">
            <h2>⚠️ 異常趨勢警報</h2>
            {% for anomaly in anomalies[:5] %}
            <div class="anomaly-alert">
                <h4>{{ anomaly.symbol }} ({{ anomaly.entity_type.upper() }})</h4>
                <p><strong>異常類型:</strong> {{ anomaly.anomaly_types|join(', ') }}</p>
                <p><strong>提及變化:</strong> {{ "%.1f"|format(anomaly.mention_change_ratio) }}x ({{ anomaly.recent_mentions }} vs {{ anomaly.previous_mentions }})</p>
                <p><strong>情緒變化:</strong> {{ "%.2f"|format(anomaly.sentiment_change) }}</p>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 活躍 Subreddits -->
        <div class="section">
            <h2>🔥 活躍 Subreddits</h2>
            <div class="subreddit-list">
                {% for subreddit in active_subreddits[:12] %}
                <div class="subreddit-item">
                    <strong>{{ subreddit.display_name }}</strong>
                    <br>
                    <small>{{ subreddit.category.upper() }} | 帖子: {{ subreddit.post_count }}</small>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 頁腳 -->
        <div class="section" style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6;">
            <p class="timestamp">Reddit 股票加密貨幣情緒監控系統 | 自動生成報告</p>
            <p class="timestamp">數據來源: Reddit API | 情緒分析: OpenRouter AI</p>
        </div>
    </div>

    <script>
        {{ javascript }}
    </script>
</body>
</html>
