"""
Reddit 數據收集器
使用 PRAW 從 Reddit 收集帖子和評論數據
"""

import praw
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from ..utils.config import Config
from ..storage.database import DatabaseManager


class RedditCollector:
    """Reddit 數據收集器"""
    
    def __init__(self):
        self.config = Config()
        self.db = DatabaseManager()
        self.reddit = None
        self.logger = self._setup_logger()
        
        # 初始化 Reddit API 連接
        self._init_reddit_connection()
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('RedditCollector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_reddit_connection(self):
        """初始化 Reddit API 連接"""
        try:
            reddit_config = self.config.get_reddit_config()
            
            self.reddit = praw.Reddit(
                client_id=reddit_config['client_id'],
                client_secret=reddit_config['client_secret'],
                user_agent=reddit_config['user_agent'],
                username=reddit_config['username'],
                password=reddit_config['password']
            )
            
            # 測試連接
            self.reddit.user.me()
            self.logger.info("✅ Reddit API 連接成功")
            
        except Exception as e:
            self.logger.error(f"❌ Reddit API 連接失敗: {e}")
            raise
    
    def test_connection(self) -> bool:
        """測試 Reddit API 連接"""
        try:
            user = self.reddit.user.me()
            self.logger.info(f"✅ 已連接到 Reddit，用戶: {user.name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Reddit 連接測試失敗: {e}")
            return False
    
    def collect_subreddit_posts(self, subreddit_name: str, limit: int = None, post_types: List[str] = None) -> List[Dict[str, Any]]:
        """收集指定 subreddit 的帖子"""
        if limit is None:
            limit = self.config.POSTS_PER_SUBREDDIT

        if post_types is None:
            post_types = ['hot']  # 默認只收集熱門帖子

        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            posts_data = []
            collected_post_ids = set()  # 避免重複

            # 獲取 subreddit ID
            subreddits = self.db.get_subreddits_by_category("stock") + self.db.get_subreddits_by_category("crypto")
            subreddit_id = None
            for sr in subreddits:
                if sr['name'] == subreddit_name:
                    subreddit_id = sr['id']
                    break

            if not subreddit_id:
                self.logger.warning(f"未找到 subreddit {subreddit_name} 的 ID")
                return []

            # 每種類型收集的帖子數量
            posts_per_type = limit // len(post_types)

            # 收集不同類型的帖子
            for post_type in post_types:
                try:
                    if post_type == 'hot':
                        posts_iterator = subreddit.hot(limit=posts_per_type)
                    elif post_type == 'new':
                        posts_iterator = subreddit.new(limit=posts_per_type)
                    elif post_type == 'top':
                        posts_iterator = subreddit.top(time_filter='day', limit=posts_per_type)
                    elif post_type == 'rising':
                        posts_iterator = subreddit.rising(limit=posts_per_type)
                    else:
                        continue

                    for post in posts_iterator:
                        try:
                            # 避免重複收集
                            if post.id in collected_post_ids:
                                continue

                            collected_post_ids.add(post.id)

                            post_data = {
                                'id': post.id,
                                'subreddit_id': subreddit_id,
                                'title': post.title,
                                'content': post.selftext if hasattr(post, 'selftext') else '',
                                'author': str(post.author) if post.author else '[deleted]',
                                'score': post.score,
                                'upvote_ratio': post.upvote_ratio,
                                'num_comments': post.num_comments,
                                'url': post.url,
                                'created_utc': datetime.fromtimestamp(post.created_utc)
                            }

                            posts_data.append(post_data)

                            # 保存到數據庫
                            self.db.models.insert_post(post_data)

                        except Exception as e:
                            self.logger.error(f"處理帖子時出錯: {e}")
                            continue

                except Exception as e:
                    self.logger.error(f"收集 {post_type} 帖子時出錯: {e}")
                    continue

                # 避免 API 限制
                time.sleep(1)

            self.logger.info(f"從 r/{subreddit_name} 收集了 {len(posts_data)} 個帖子 (類型: {post_types})")
            return posts_data
            
        except Exception as e:
            self.logger.error(f"收集 r/{subreddit_name} 帖子時出錯: {e}")
            return []
    
    def collect_post_comments(self, post_id: str, limit: int = None) -> List[Dict[str, Any]]:
        """收集指定帖子的評論"""
        if limit is None:
            limit = self.config.COMMENTS_PER_POST
        
        try:
            submission = self.reddit.submission(id=post_id)
            submission.comments.replace_more(limit=0)  # 展開所有評論
            
            comments_data = []
            comment_count = 0
            
            for comment in submission.comments.list():
                if comment_count >= limit:
                    break
                
                try:
                    if hasattr(comment, 'body') and comment.body != '[deleted]':
                        comment_data = {
                            'id': comment.id,
                            'post_id': post_id,
                            'parent_id': comment.parent_id if comment.parent_id != post_id else None,
                            'content': comment.body,
                            'author': str(comment.author) if comment.author else '[deleted]',
                            'score': comment.score,
                            'created_utc': datetime.fromtimestamp(comment.created_utc)
                        }
                        
                        comments_data.append(comment_data)
                        comment_count += 1
                        
                        # 保存到數據庫
                        self._insert_comment(comment_data)
                
                except Exception as e:
                    self.logger.error(f"處理評論時出錯: {e}")
                    continue
            
            self.logger.info(f"從帖子 {post_id} 收集了 {len(comments_data)} 個評論")
            return comments_data
            
        except Exception as e:
            self.logger.error(f"收集帖子 {post_id} 評論時出錯: {e}")
            return []
    
    def _insert_comment(self, comment_data: Dict[str, Any]) -> bool:
        """插入評論到數據庫"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO comments 
                (id, post_id, parent_id, content, author, score, created_utc, collected_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                comment_data['id'],
                comment_data['post_id'],
                comment_data.get('parent_id'),
                comment_data['content'],
                comment_data.get('author', ''),
                comment_data.get('score', 0),
                comment_data['created_utc'],
                datetime.now()
            ))
            
            conn.commit()
            return True
        except Exception as e:
            self.logger.error(f"插入評論時出錯: {e}")
            return False
        finally:
            conn.close()
    
    def collect_all_subreddits(self, category: str = None) -> Dict[str, int]:
        """收集所有活躍 subreddits 的數據"""
        results = {"posts": 0, "comments": 0, "errors": 0}
        
        if category:
            subreddits = self.db.get_subreddits_by_category(category)
        else:
            subreddits = self.db.models.get_active_subreddits()
        
        self.logger.info(f"開始收集 {len(subreddits)} 個 subreddits 的數據")
        
        for subreddit in subreddits:
            try:
                # 收集帖子
                posts = self.collect_subreddit_posts(subreddit['name'])
                results["posts"] += len(posts)
                
                # 為每個帖子收集評論（限制數量以避免過多 API 調用）
                for post in posts[:5]:  # 只為前5個帖子收集評論
                    comments = self.collect_post_comments(post['id'], limit=10)
                    results["comments"] += len(comments)
                
                # 避免 API 限制
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"收集 {subreddit['name']} 時出錯: {e}")
                results["errors"] += 1
                continue
        
        self.logger.info(f"收集完成: {results['posts']} 個帖子, {results['comments']} 個評論, {results['errors']} 個錯誤")
        return results
    
    def get_subreddit_info(self, subreddit_name: str) -> Optional[Dict[str, Any]]:
        """獲取 subreddit 信息"""
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            return {
                'name': subreddit.display_name,
                'title': subreddit.title,
                'description': subreddit.description,
                'subscribers': subreddit.subscribers,
                'active_users': subreddit.active_user_count,
                'created_utc': datetime.fromtimestamp(subreddit.created_utc),
                'over18': subreddit.over18,
                'public': subreddit.subreddit_type == 'public'
            }
            
        except Exception as e:
            self.logger.error(f"獲取 r/{subreddit_name} 信息時出錯: {e}")
            return None
    
    def update_subreddit_stats(self):
        """更新所有 subreddit 的統計信息"""
        subreddits = self.db.models.get_active_subreddits()
        
        for subreddit in subreddits:
            try:
                info = self.get_subreddit_info(subreddit['name'])
                if info:
                    # 更新訂閱者數量
                    conn = self.db.models.get_connection()
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        UPDATE subreddits 
                        SET subscribers = ?, updated_at = ?
                        WHERE id = ?
                    ''', (info['subscribers'], datetime.now(), subreddit['id']))
                    
                    conn.commit()
                    conn.close()
                
                time.sleep(1)  # 避免 API 限制
                
            except Exception as e:
                self.logger.error(f"更新 {subreddit['name']} 統計時出錯: {e}")
                continue
        
        self.logger.info("Subreddit 統計信息更新完成")

    def bulk_collect_data(self, target_posts: int = None, max_subreddits: int = None) -> Dict[str, int]:
        """批量收集大量數據"""
        if target_posts is None:
            target_posts = self.config.TARGET_TOTAL_POSTS

        if max_subreddits is None:
            max_subreddits = self.config.MAX_SUBREDDITS_FOR_BULK

        self.logger.info(f"🚀 開始批量收集數據，目標: {target_posts} 個帖子")

        results = {"posts": 0, "comments": 0, "errors": 0, "subreddits_processed": 0}

        # 獲取所有活躍的 subreddits
        all_subreddits = self.db.models.get_active_subreddits()

        # 按訂閱者數量排序，優先收集大群組
        all_subreddits.sort(key=lambda x: x.get('subscribers', 0), reverse=True)

        # 限制 subreddit 數量
        selected_subreddits = all_subreddits[:max_subreddits]

        # 計算每個 subreddit 應該收集的帖子數量
        posts_per_subreddit = max(50, target_posts // len(selected_subreddits))

        self.logger.info(f"選擇了 {len(selected_subreddits)} 個 subreddits，每個收集 {posts_per_subreddit} 個帖子")

        for i, subreddit in enumerate(selected_subreddits, 1):
            try:
                self.logger.info(f"📥 [{i}/{len(selected_subreddits)}] 收集 r/{subreddit['name']} (訂閱者: {subreddit.get('subscribers', 0):,})")

                # 收集多種類型的帖子以增加多樣性
                post_types = ['hot', 'new', 'top', 'rising']
                posts = self.collect_subreddit_posts(
                    subreddit['name'],
                    limit=posts_per_subreddit,
                    post_types=post_types
                )
                results["posts"] += len(posts)
                results["subreddits_processed"] += 1

                # 為部分帖子收集評論
                comments_collected = 0
                for post in posts[:min(20, len(posts))]:  # 只為前20個帖子收集評論
                    try:
                        comments = self.collect_post_comments(
                            post['id'],
                            limit=self.config.BULK_COMMENTS_PER_POST
                        )
                        comments_collected += len(comments)
                    except Exception as e:
                        self.logger.error(f"收集帖子 {post['id']} 評論時出錯: {e}")
                        continue

                results["comments"] += comments_collected

                self.logger.info(f"  ✅ 完成: {len(posts)} 個帖子, {comments_collected} 個評論")

                # 檢查是否達到目標
                if results["posts"] >= target_posts:
                    self.logger.info(f"🎯 達到目標帖子數量: {results['posts']}")
                    break

                # 避免 API 限制
                time.sleep(3)

            except Exception as e:
                self.logger.error(f"收集 {subreddit['name']} 時出錯: {e}")
                results["errors"] += 1
                continue

        self.logger.info(f"🎉 批量收集完成: {results}")
        return results

    def collect_diverse_posts(self, subreddit_name: str, total_limit: int = 200) -> List[Dict[str, Any]]:
        """收集多樣化的帖子（包含不同時間段和類型）"""
        all_posts = []
        collected_ids = set()

        # 分配給不同類型的帖子數量
        type_limits = {
            'hot': total_limit // 4,
            'new': total_limit // 4,
            'top_day': total_limit // 6,
            'top_week': total_limit // 6,
            'top_month': total_limit // 6,
            'rising': total_limit // 6
        }

        try:
            subreddit = self.reddit.subreddit(subreddit_name)

            # 獲取 subreddit ID
            subreddits = self.db.get_subreddits_by_category("stock") + self.db.get_subreddits_by_category("crypto")
            subreddit_id = None
            for sr in subreddits:
                if sr['name'] == subreddit_name:
                    subreddit_id = sr['id']
                    break

            if not subreddit_id:
                self.logger.warning(f"未找到 subreddit {subreddit_name} 的 ID")
                return []

            # 收集不同類型的帖子
            post_sources = [
                ('hot', subreddit.hot(limit=type_limits['hot'])),
                ('new', subreddit.new(limit=type_limits['new'])),
                ('top_day', subreddit.top(time_filter='day', limit=type_limits['top_day'])),
                ('top_week', subreddit.top(time_filter='week', limit=type_limits['top_week'])),
                ('top_month', subreddit.top(time_filter='month', limit=type_limits['top_month'])),
                ('rising', subreddit.rising(limit=type_limits['rising']))
            ]

            for source_type, posts_iterator in post_sources:
                try:
                    for post in posts_iterator:
                        if post.id in collected_ids:
                            continue

                        collected_ids.add(post.id)

                        post_data = {
                            'id': post.id,
                            'subreddit_id': subreddit_id,
                            'title': post.title,
                            'content': post.selftext if hasattr(post, 'selftext') else '',
                            'author': str(post.author) if post.author else '[deleted]',
                            'score': post.score,
                            'upvote_ratio': post.upvote_ratio,
                            'num_comments': post.num_comments,
                            'url': post.url,
                            'created_utc': datetime.fromtimestamp(post.created_utc),
                            'source_type': source_type
                        }

                        all_posts.append(post_data)

                        # 保存到數據庫
                        self.db.models.insert_post(post_data)

                except Exception as e:
                    self.logger.error(f"收集 {source_type} 帖子時出錯: {e}")
                    continue

                # 避免 API 限制
                time.sleep(1)

            self.logger.info(f"從 r/{subreddit_name} 收集了 {len(all_posts)} 個多樣化帖子")
            return all_posts

        except Exception as e:
            self.logger.error(f"收集 r/{subreddit_name} 多樣化帖子時出錯: {e}")
            return []
