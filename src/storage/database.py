"""
數據庫操作類
提供高級數據庫操作接口
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from .models import DatabaseModels


class DatabaseManager:
    """數據庫管理器"""
    
    def __init__(self, db_path: str = "data/reddit_monitor.db"):
        self.db_path = db_path
        self.models = DatabaseModels(db_path)
    
    def setup_default_subreddits(self):
        """設置默認的監控 subreddits"""
        
        # 股票相關的 subreddits
        stock_subreddits = [
            ("stocks", "r/stocks", 4500000),
            ("investing", "r/investing", 1800000),
            ("SecurityAnalysis", "r/SecurityAnalysis", 180000),
            ("ValueInvesting", "r/ValueInvesting", 220000),
            ("pennystocks", "r/pennystocks", 220000),
            ("StockMarket", "r/StockMarket", 4200000),
            ("wallstreetbets", "r/wallstreetbets", 15000000),
            ("options", "r/options", 550000),
            ("dividends", "r/dividends", 180000),
            ("financialindependence", "r/financialindependence", 1400000),
            ("SecurityAnalysis", "r/SecurityAnalysis", 180000),
            ("investing_discussion", "r/investing_discussion", 45000),
            ("StockMarketChat", "r/StockMarketChat", 25000),
            ("smallstreetbets", "r/smallstreetbets", 350000),
            ("RobinHood", "r/RobinHood", 350000),
            ("trading", "r/trading", 180000),
            ("daytrading", "r/daytrading", 350000),
            ("swingtrading", "r/swingtrading", 120000),
            ("SecurityAnalysis", "r/SecurityAnalysis", 180000),
            ("ValueInvesting", "r/ValueInvesting", 220000),
            ("financialindependence", "r/financialindependence", 1400000),
            ("Fire", "r/Fire", 850000),
            ("personalfinance", "r/personalfinance", 16000000),
            ("investing", "r/investing", 1800000),
            ("SecurityAnalysis", "r/SecurityAnalysis", 180000)
        ]
        
        # 加密貨幣相關的 subreddits
        crypto_subreddits = [
            ("CryptoCurrency", "r/CryptoCurrency", 6800000),
            ("Bitcoin", "r/Bitcoin", 4900000),
            ("ethereum", "r/ethereum", 1200000),
            ("dogecoin", "r/dogecoin", 2300000),
            ("CryptoMarkets", "r/CryptoMarkets", 1800000),
            ("altcoin", "r/altcoin", 180000),
            ("CryptoMoonShots", "r/CryptoMoonShots", 1800000),
            ("SatoshiStreetBets", "r/SatoshiStreetBets", 500000),
            ("binance", "r/binance", 850000),
            ("coinbase", "r/coinbase", 350000),
            ("defi", "r/defi", 280000),
            ("NFT", "r/NFT", 280000),
            ("solana", "r/solana", 220000),
            ("cardano", "r/cardano", 650000),
            ("litecoin", "r/litecoin", 350000),
            ("ripple", "r/ripple", 280000),
            ("polkadot", "r/polkadot", 120000),
            ("chainlink", "r/chainlink", 180000),
            ("stellar", "r/stellar", 220000),
            ("Monero", "r/Monero", 280000),
            ("btc", "r/btc", 850000),
            ("ethtrader", "r/ethtrader", 1200000),
            ("CryptoCurrencyTrading", "r/CryptoCurrencyTrading", 180000),
            ("CryptoTechnology", "r/CryptoTechnology", 350000),
            ("CryptoCurrencies", "r/CryptoCurrencies", 120000)
        ]
        
        # 插入股票 subreddits
        for name, display_name, subscribers in stock_subreddits:
            try:
                self.models.insert_subreddit(name, display_name, "stock", subscribers)
            except Exception as e:
                print(f"Error inserting stock subreddit {name}: {e}")
        
        # 插入加密貨幣 subreddits
        for name, display_name, subscribers in crypto_subreddits:
            try:
                self.models.insert_subreddit(name, display_name, "crypto", subscribers)
            except Exception as e:
                print(f"Error inserting crypto subreddit {name}: {e}")
        
        print(f"設置完成：{len(stock_subreddits)} 個股票群組，{len(crypto_subreddits)} 個加密貨幣群組")
    
    def get_subreddits_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根據類別獲取 subreddits"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM subreddits 
            WHERE category = ? AND is_active = 1
            ORDER BY subscribers DESC
        ''', (category,))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def get_trending_entities(self, entity_type: str, time_period: str = "24h", limit: int = 10) -> List[Dict[str, Any]]:
        """獲取趨勢實體（股票或加密貨幣）"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT symbol, mention_count, avg_sentiment_score, trend_score
            FROM trends 
            WHERE entity_type = ? AND time_period = ?
            ORDER BY trend_score DESC
            LIMIT ?
        ''', (entity_type, time_period, limit))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def get_sentiment_summary(self, hours: int = 24) -> Dict[str, Any]:
        """獲取情緒分析摘要"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        # 獲取總體情緒分布
        cursor.execute('''
            SELECT sentiment_label, COUNT(*) as count
            FROM sentiment_analysis 
            WHERE analyzed_at >= datetime('now', '-{} hours')
            GROUP BY sentiment_label
        '''.format(hours))
        
        sentiment_distribution = dict(cursor.fetchall())
        
        # 獲取平均情緒分數
        cursor.execute('''
            SELECT AVG(sentiment_score) as avg_sentiment
            FROM sentiment_analysis 
            WHERE analyzed_at >= datetime('now', '-{} hours')
        '''.format(hours))
        
        avg_sentiment = cursor.fetchone()[0] or 0.0
        
        conn.close()
        
        return {
            "sentiment_distribution": sentiment_distribution,
            "average_sentiment": avg_sentiment,
            "total_analyzed": sum(sentiment_distribution.values())
        }
    
    def get_top_posts_by_engagement(self, hours: int = 24, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取高參與度的帖子"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, s.name as subreddit_name, s.category,
                   (p.score + p.num_comments * 2) as engagement_score
            FROM posts p
            JOIN subreddits s ON p.subreddit_id = s.id
            WHERE p.created_utc >= datetime('now', '-{} hours')
            ORDER BY engagement_score DESC
            LIMIT ?
        '''.format(hours), (limit,))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def insert_entity_mention(self, content_id: str, content_type: str, 
                            entity_type: str, symbol: str, name: str = None,
                            confidence: float = 1.0, context: str = None) -> bool:
        """插入實體提及記錄"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO entities 
                (content_id, content_type, entity_type, symbol, name, confidence, context, extracted_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                content_id, content_type, entity_type, symbol, name,
                confidence, context, datetime.now()
            ))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"Error inserting entity mention: {e}")
            return False
        finally:
            conn.close()
    
    def calculate_trends(self, time_period: str = "24h") -> bool:
        """計算趨勢數據"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        # 確定時間範圍
        if time_period == "1h":
            hours = 1
        elif time_period == "24h":
            hours = 24
        elif time_period == "7d":
            hours = 168
        else:
            hours = 24
        
        try:
            # 清除舊的趨勢數據
            cursor.execute('''
                DELETE FROM trends
                WHERE time_period = ? AND calculated_at < datetime('now', '-2 hours')
            ''', (time_period,))

            # 清除當前時段的舊數據以避免重複
            cursor.execute('''
                DELETE FROM trends
                WHERE time_period = ? AND calculated_at >= datetime('now', '-1 hour')
            ''', (time_period,))
            
            # 計算每個實體的趨勢
            cursor.execute('''
                SELECT 
                    e.entity_type,
                    e.symbol,
                    COUNT(*) as mention_count,
                    SUM(CASE WHEN sa.sentiment_label = 'positive' THEN 1 ELSE 0 END) as positive_count,
                    SUM(CASE WHEN sa.sentiment_label = 'negative' THEN 1 ELSE 0 END) as negative_count,
                    SUM(CASE WHEN sa.sentiment_label = 'neutral' THEN 1 ELSE 0 END) as neutral_count,
                    AVG(sa.sentiment_score) as avg_sentiment
                FROM entities e
                LEFT JOIN sentiment_analysis sa ON e.content_id = sa.content_id AND e.content_type = sa.content_type
                WHERE e.extracted_at >= datetime('now', '-{} hours')
                GROUP BY e.entity_type, e.symbol
                HAVING mention_count >= 3
                ORDER BY mention_count DESC
            '''.format(hours))
            
            trends_data = cursor.fetchall()
            
            # 插入趨勢數據
            for row in trends_data:
                entity_type, symbol, mention_count, positive_count, negative_count, neutral_count, avg_sentiment = row
                
                # 計算趨勢分數（綜合考慮提及次數和情緒）
                sentiment_multiplier = max(0.1, (avg_sentiment or 0) + 1)  # 0.1 到 2.0
                trend_score = mention_count * sentiment_multiplier
                
                cursor.execute('''
                    INSERT INTO trends
                    (entity_type, symbol, time_period, mention_count,
                     positive_sentiment_count, negative_sentiment_count, neutral_sentiment_count,
                     avg_sentiment_score, trend_score, calculated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    entity_type, symbol, time_period, mention_count,
                    positive_count or 0, negative_count or 0, neutral_count or 0,
                    avg_sentiment or 0.0, trend_score, datetime.now()
                ))
            
            conn.commit()
            print(f"計算了 {len(trends_data)} 個實體的 {time_period} 趨勢數據")
            return True
            
        except Exception as e:
            print(f"Error calculating trends: {e}")
            return False
        finally:
            conn.close()
    
    def cleanup_old_data(self, days: int = 30):
        """清理舊數據"""
        conn = self.models.get_connection()
        cursor = conn.cursor()
        
        try:
            # 清理舊的帖子和評論
            cursor.execute('''
                DELETE FROM posts 
                WHERE created_utc < datetime('now', '-{} days')
            '''.format(days))
            
            cursor.execute('''
                DELETE FROM comments 
                WHERE created_utc < datetime('now', '-{} days')
            '''.format(days))
            
            # 清理舊的分析結果
            cursor.execute('''
                DELETE FROM sentiment_analysis 
                WHERE analyzed_at < datetime('now', '-{} days')
            '''.format(days))
            
            cursor.execute('''
                DELETE FROM entities 
                WHERE extracted_at < datetime('now', '-{} days')
            '''.format(days))
            
            cursor.execute('''
                DELETE FROM trends 
                WHERE calculated_at < datetime('now', '-7 days')
            ''')
            
            conn.commit()
            print(f"清理了 {days} 天前的舊數據")
            
        except Exception as e:
            print(f"Error cleaning up old data: {e}")
        finally:
            conn.close()
