"""
數據庫模型定義
定義了 Reddit 股票和加密貨幣監控系統的所有數據表結構
"""

import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
import json


class DatabaseModels:
    """數據庫模型管理類"""
    
    def __init__(self, db_path: str = "data/reddit_monitor.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """獲取數據庫連接"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """初始化數據庫表結構"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 創建 subreddits 表 - 監控的 Reddit 群組
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS subreddits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                category TEXT NOT NULL,  -- 'stock' or 'crypto'
                subscribers INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 創建 posts 表 - Reddit 帖子
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id TEXT PRIMARY KEY,  -- Reddit post ID
                subreddit_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                author TEXT,
                score INTEGER DEFAULT 0,
                upvote_ratio REAL DEFAULT 0.0,
                num_comments INTEGER DEFAULT 0,
                url TEXT,
                created_utc TIMESTAMP NOT NULL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (subreddit_id) REFERENCES subreddits (id)
            )
        ''')
        
        # 創建 comments 表 - Reddit 評論
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id TEXT PRIMARY KEY,  -- Reddit comment ID
                post_id TEXT NOT NULL,
                parent_id TEXT,  -- 父評論 ID，如果是頂級評論則為 NULL
                content TEXT NOT NULL,
                author TEXT,
                score INTEGER DEFAULT 0,
                created_utc TIMESTAMP NOT NULL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (post_id) REFERENCES posts (id)
            )
        ''')
        
        # 創建 sentiment_analysis 表 - 情緒分析結果
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sentiment_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content_id TEXT NOT NULL,  -- post_id 或 comment_id
                content_type TEXT NOT NULL,  -- 'post' or 'comment'
                sentiment_score REAL NOT NULL,  -- -1.0 到 1.0
                sentiment_label TEXT NOT NULL,  -- 'positive', 'negative', 'neutral'
                confidence REAL NOT NULL,  -- 0.0 到 1.0
                analysis_model TEXT NOT NULL,  -- 使用的分析模型
                raw_response TEXT,  -- 原始 API 響應
                analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 創建 entities 表 - 提取的股票/加密貨幣實體
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content_id TEXT NOT NULL,  -- post_id 或 comment_id
                content_type TEXT NOT NULL,  -- 'post' or 'comment'
                entity_type TEXT NOT NULL,  -- 'stock' or 'crypto'
                symbol TEXT NOT NULL,  -- 股票代碼或加密貨幣符號
                name TEXT,  -- 完整名稱
                confidence REAL NOT NULL,  -- 0.0 到 1.0
                context TEXT,  -- 提及的上下文
                extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 創建 trends 表 - 趨勢分析結果
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trends (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,  -- 'stock' or 'crypto'
                symbol TEXT NOT NULL,
                time_period TEXT NOT NULL,  -- '1h', '24h', '7d'
                mention_count INTEGER NOT NULL,
                positive_sentiment_count INTEGER NOT NULL,
                negative_sentiment_count INTEGER NOT NULL,
                neutral_sentiment_count INTEGER NOT NULL,
                avg_sentiment_score REAL NOT NULL,
                trend_score REAL NOT NULL,  -- 綜合趨勢分數
                calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 創建 reports 表 - 生成的報告
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT NOT NULL,  -- 'hourly', 'daily', 'weekly'
                file_path TEXT NOT NULL,
                summary TEXT,
                top_stocks TEXT,  -- JSON 格式
                top_cryptos TEXT,  -- JSON 格式
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 創建索引以提高查詢性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_posts_subreddit ON posts(subreddit_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_posts_created ON posts(created_utc)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_comments_post ON comments(post_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sentiment_content ON sentiment_analysis(content_id, content_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entities_symbol ON entities(symbol, entity_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_trends_symbol ON trends(symbol, time_period)')
        
        conn.commit()
        conn.close()
    
    def insert_subreddit(self, name: str, display_name: str, category: str, subscribers: int = 0) -> int:
        """插入新的 subreddit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO subreddits (name, display_name, category, subscribers, updated_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, display_name, category, subscribers, datetime.now()))
        
        subreddit_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return subreddit_id
    
    def get_active_subreddits(self) -> List[Dict[str, Any]]:
        """獲取所有活躍的 subreddits"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, display_name, category, subscribers
            FROM subreddits 
            WHERE is_active = 1
            ORDER BY subscribers DESC
        ''')
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def insert_post(self, post_data: Dict[str, Any]) -> bool:
        """插入新的 post"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO posts 
                (id, subreddit_id, title, content, author, score, upvote_ratio, 
                 num_comments, url, created_utc, collected_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                post_data['id'],
                post_data['subreddit_id'],
                post_data['title'],
                post_data.get('content', ''),
                post_data.get('author', ''),
                post_data.get('score', 0),
                post_data.get('upvote_ratio', 0.0),
                post_data.get('num_comments', 0),
                post_data.get('url', ''),
                post_data['created_utc'],
                datetime.now()
            ))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"Error inserting post: {e}")
            return False
        finally:
            conn.close()
    
    def insert_sentiment(self, content_id: str, content_type: str, 
                        sentiment_score: float, sentiment_label: str, 
                        confidence: float, analysis_model: str, 
                        raw_response: str = None) -> bool:
        """插入情緒分析結果"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO sentiment_analysis 
                (content_id, content_type, sentiment_score, sentiment_label, 
                 confidence, analysis_model, raw_response, analyzed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                content_id, content_type, sentiment_score, sentiment_label,
                confidence, analysis_model, raw_response, datetime.now()
            ))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"Error inserting sentiment: {e}")
            return False
        finally:
            conn.close()
    
    def get_recent_posts(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """獲取最近的帖子"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, s.name as subreddit_name, s.category
            FROM posts p
            JOIN subreddits s ON p.subreddit_id = s.id
            WHERE p.created_utc >= datetime('now', '-{} hours')
            ORDER BY p.created_utc DESC
            LIMIT ?
        '''.format(hours), (limit,))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
