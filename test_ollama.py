#!/usr/bin/env python3
"""
測試 Ollama 情緒分析器
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analyzers.ollama_sentiment_analyzer import OllamaSentimentAnalyzer


def test_ollama_analyzer():
    """測試 Ollama 分析器"""
    print("🧪 測試 Ollama 情緒分析器...")
    
    # 創建分析器
    analyzer = OllamaSentimentAnalyzer()
    
    # 測試樣本
    test_samples = [
        {
            'content': 'Apple stock is going to the moon! Great earnings report!',
            'expected': 'positive'
        },
        {
            'content': 'Bitcoin is crashing hard. Sell everything now!',
            'expected': 'negative'
        },
        {
            'content': 'The market opened at 100 points today.',
            'expected': 'neutral'
        }
    ]
    
    print("\n📊 測試結果:")
    print("=" * 60)
    
    for i, sample in enumerate(test_samples, 1):
        print(f"\n測試 {i}: {sample['content'][:50]}...")
        
        result = analyzer.analyze_sentiment(
            content=sample['content'],
            content_id=f"test_{i}",
            content_type='test'
        )
        
        if result:
            print(f"  情緒: {result['sentiment_label']} ({result['sentiment_score']:.2f})")
            print(f"  信心度: {result['confidence']:.2f}")
            print(f"  解釋: {result.get('explanation', 'N/A')}")
            
            # 檢查預期結果
            if result['sentiment_label'] == sample['expected']:
                print("  ✅ 結果正確")
            else:
                print(f"  ⚠️ 預期 {sample['expected']}，實際 {result['sentiment_label']}")
        else:
            print("  ❌ 分析失敗")
        
        print("-" * 40)
    
    print("\n🎯 測試完成！")


if __name__ == "__main__":
    test_ollama_analyzer()