# Reddit 股票加密貨幣情緒監控系統 🚀

一個 24/7 運行的智能監控系統，實時分析 Reddit 上 50-100 個群組的股票和加密貨幣討論，提供市場情緒分析和趨勢報告。

## ✨ 功能特色

- 🔄 **自動數據收集**: 監控 50+ 股票和加密貨幣相關的 Reddit 群組
- 🎭 **AI 情緒分析**: 使用 OpenRouter API 分析帖子和評論的情緒
- 🔍 **智能實體提取**: 自動識別股票代碼和加密貨幣符號
- 📈 **趨勢分析**: 計算熱度分數和情緒趨勢
- 📊 **實時報告**: 每小時生成 HTML 和 JSON 格式報告
- ⚠️ **異常檢測**: 識別突然的熱度或情緒變化
- 🕐 **定時調度**: 使用 APScheduler 管理所有任務

## 🏗️ 系統架構

```
reddit-research/
├── src/
│   ├── collectors/          # Reddit 數據收集
│   ├── analyzers/           # 情緒分析和實體提取
│   ├── storage/             # 數據庫模型和操作
│   ├── schedulers/          # 任務調度系統
│   ├── reporters/           # 報告生成
│   └── utils/               # 配置和工具
├── data/                    # SQLite 數據庫
├── reports/                 # 生成的報告
├── tests/                   # 測試文件
├── main.py                  # 主程序入口
└── requirements.txt         # 依賴包
```

## 🚀 快速開始

### 1. 環境設置

```bash
# 克隆項目
git clone <repository-url>
cd reddit-research

# 安裝依賴
pip install -r requirements.txt
```

### 2. 配置 API 憑證

創建 `.env` 文件並添加以下配置：

```env
# Reddit API 憑證
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=your_app_name:1.0 (by /u/your_username)
REDDIT_USERNAME=your_reddit_username
REDDIT_PASSWORD=your_reddit_password

# OpenRouter API 憑證
OPENROUTER_API_KEY=your_openrouter_api_key
```

### 3. 運行系統

```bash
# 驗證配置
python main.py --validate

# 運行測試模式
python main.py --test

# 啟動完整監控系統
python main.py
```

## 📋 使用方法

### 命令行選項

```bash
python main.py                    # 啟動完整的 24/7 監控系統
python main.py --test             # 運行測試模式
python main.py --collect          # 只運行數據收集
python main.py --analyze          # 只運行分析
python main.py --report           # 只生成報告
python main.py --validate         # 驗證系統配置
```

### 監控的 Reddit 群組

**股票相關 (25+ 群組):**
- r/stocks, r/investing, r/wallstreetbets
- r/SecurityAnalysis, r/ValueInvesting
- r/pennystocks, r/options, r/dividends
- 等等...

**加密貨幣相關 (25+ 群組):**
- r/CryptoCurrency, r/Bitcoin, r/ethereum
- r/dogecoin, r/CryptoMarkets, r/defi
- r/solana, r/cardano, r/NFT
- 等等...

## 📊 報告功能

系統會自動生成以下報告：

### 每小時報告
- 熱門股票和加密貨幣 (前 10 名)
- 市場情緒分析
- 活動統計
- 異常趨勢警報

### 每日/每週報告
- 更長時間範圍的趨勢分析
- 歷史數據對比
- 詳細的情緒變化

### 報告格式
- **HTML**: 美觀的網頁報告，包含圖表和統計
- **JSON**: 結構化數據，便於 API 集成

## 🔧 系統組件

### 數據收集器 (Reddit Collector)
- 使用 PRAW 庫連接 Reddit API
- 收集帖子標題、內容和評論
- 處理 API 限制和錯誤重試

### 情緒分析器 (Sentiment Analyzer)
- 使用 OpenRouter API 調用 Claude 模型
- 分析文本情緒 (-1.0 到 1.0)
- 提供信心度評分

### 實體提取器 (Entity Extractor)
- 正則表達式匹配已知符號
- LLM 輔助提取新符號
- 上下文分析和信心度評估

### 趨勢分析器 (Trend Analyzer)
- 計算提及次數和情緒趨勢
- 異常檢測算法
- 綜合趨勢分數計算

### 任務調度器 (Task Scheduler)
- 每小時數據收集和分析
- 每日數據清理
- 靈活的任務配置

## 📈 監控指標

- **提及次數**: 實體在指定時間內的討論頻率
- **情緒分數**: 平均情緒值 (-1.0 負面 到 1.0 正面)
- **趨勢分數**: 綜合熱度指標 (提及次數 × 情緒倍數)
- **信心度**: AI 分析結果的可信度
- **異常檢測**: 突然的熱度或情緒變化

## 🛠️ 技術棧

- **Python 3.9+**: 主要編程語言
- **PRAW**: Reddit API 客戶端
- **OpenRouter**: AI 情緒分析 API
- **SQLite**: 本地數據存儲
- **APScheduler**: 任務調度
- **Jinja2**: HTML 模板引擎
- **Pandas**: 數據處理

## 📝 測試

```bash
# 運行連接測試
python test_reddit_connection.py

# 運行分析器測試
python test_analyzers.py

# 運行完整系統測試
python test_complete_system.py
```

## 🔒 安全注意事項

- 不要將 `.env` 文件提交到版本控制
- 定期輪換 API 密鑰
- 監控 API 使用量和成本
- 遵守 Reddit API 使用條款

## 📊 性能優化

- 智能緩存避免重複分析
- 批處理 API 調用
- 數據庫索引優化
- 自動清理舊數據

## 📚 詳細文檔

- [📖 安裝指南](docs/INSTALLATION.md) - 詳細的安裝和配置步驟
- [🏗️ 系統架構](docs/ARCHITECTURE.md) - 深入的架構設計文檔
- [🔌 API 文檔](docs/API.md) - 完整的 API 參考手冊
- [📋 使用指南](docs/USAGE.md) - 詳細的使用說明和最佳實踐

## 🛠️ 實用腳本

### 數據收集
- `scripts/data/collect_large_dataset.py` - 批量收集大量數據
- `scripts/data/continuous_monitor.py` - 24/7 持續監控
- `scripts/data/check_database_stats.py` - 檢查數據庫統計

### 分析工具
- `scripts/analysis/debug_trends.py` - 調試趨勢計算
- `scripts/analysis/recalculate_trends.py` - 重新計算趨勢
- `scripts/analysis/test_smart_validation.py` - 測試智能驗證

### 維護工具
- `scripts/maintenance/generate_report_now.py` - 立即生成報告
- `scripts/maintenance/clean_invalid_entities.py` - 清理無效實體

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📄 許可證

MIT License

## 📞 支持

如有問題，請創建 GitHub Issue 或聯繫開發團隊。

---

**⚡ 開始監控 Reddit 股票和加密貨幣情緒，獲得市場洞察！**
