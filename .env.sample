# Reddit API 憑證
# 請到 https://www.reddit.com/prefs/apps 創建應用並獲取以下憑證
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=RedditMonitor:1.0 (by /u/your_username)
REDDIT_USERNAME=your_reddit_username
REDDIT_PASSWORD=your_reddit_password

# 情緒分析配置
# 使用本地 Ollama 模型，無需 API 密鑰，節省成本
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:14b

# OpenRouter API 憑證（已替換為 Ollama，可選）
# OPENROUTER_API_KEY=your_openrouter_api_key

# 系統配置（可選）
# 數據庫文件路徑
DATABASE_PATH=data/reddit_monitoring.db

# 報告輸出目錄
REPORTS_DIR=reports

# 日誌級別 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# API 請求延遲（秒）
API_DELAY=1.0

# 最大重試次數
MAX_RETRIES=3