#!/usr/bin/env python3
"""
使用 Ollama 啟動 Reddit 監控系統
專為本地 AI 模型優化
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_ollama():
    """檢查 Ollama 是否運行"""
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama 服務正常運行")
            
            # 檢查 qwen3:14b 模型
            if "qwen3:14b" in result.stdout:
                print("✅ Qwen3:14B 模型可用")
                return True
            else:
                print("❌ Qwen3:14B 模型不可用")
                print("請運行: ollama pull qwen3:14b")
                return False
        else:
            print("❌ Ollama 服務未運行")
            return False
    except Exception as e:
        print(f"❌ 檢查 Ollama 失敗: {e}")
        return False

def check_system():
    """檢查系統前置條件"""
    print("🔍 檢查系統狀態...")
    
    # 檢查虛擬環境
    venv_path = Path("venv")
    if not venv_path.exists():
        print("❌ 虛擬環境不存在")
        return False
    
    # 檢查 .env 文件
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env 文件不存在，請複製 .env.sample 到 .env")
        return False
    
    # 檢查 Ollama
    if not check_ollama():
        return False
    
    return True

def start_monitoring():
    """啟動監控系統"""
    print("🚀 啟動 Reddit 股票加密貨幣監控系統 (Ollama 版本)")
    print("💰 使用本地 AI 模型，節省 API 成本")
    print("=" * 60)
    
    if not check_system():
        print("❌ 系統檢查失敗，請修復問題後重試")
        return
    
    try:
        print("📊 系統正在啟動...")
        print("🤖 使用 Ollama Qwen3:14B 進行情緒分析")
        print("🔄 監控將每小時收集數據並生成報告")
        print("📈 報告將保存在 reports/ 目錄中")
        print("⏹️  按 Ctrl+C 停止監控")
        print("=" * 60)
        
        # 啟動系統
        cmd = "source venv/bin/activate && python main.py"
        subprocess.run(cmd, shell=True, cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n⏹️  監控已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")

def show_status():
    """顯示系統狀態"""
    print("📊 Reddit 監控系統狀態")
    print("=" * 40)
    
    # 檢查 Ollama
    print("🤖 AI 模型狀態:")
    if check_ollama():
        print("  ✅ Ollama 服務運行中")
        print("  ✅ Qwen3:14B 模型可用")
    else:
        print("  ❌ Ollama 或模型不可用")
    
    # 檢查數據庫
    db_path = Path("data/reddit_monitor.db")
    if db_path.exists():
        print(f"  ✅ 數據庫存在 ({db_path.stat().st_size / 1024 / 1024:.1f} MB)")
    else:
        print("  ⚠️ 數據庫不存在")
    
    # 檢查報告
    reports_path = Path("reports")
    if reports_path.exists():
        reports = list(reports_path.glob("*.html"))
        print(f"  📊 已生成 {len(reports)} 個報告")
    else:
        print("  ⚠️ 尚未生成報告")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--status":
            show_status()
        elif sys.argv[1] == "--test":
            os.system("source venv/bin/activate && python main.py --test")
        elif sys.argv[1] == "--validate":
            os.system("source venv/bin/activate && python main.py --validate")
        else:
            print("❌ 未知參數")
            print("使用方法:")
            print("  python start_monitoring_ollama.py          # 啟動監控")
            print("  python start_monitoring_ollama.py --test   # 測試模式")
            print("  python start_monitoring_ollama.py --status # 查看狀態")
    else:
        start_monitoring()