# 安裝指南

## 📋 系統要求

### 硬件要求
- **CPU**: 2核心以上
- **內存**: 4GB RAM 以上
- **存儲**: 10GB 可用空間
- **網絡**: 穩定的互聯網連接

### 軟件要求
- **操作系統**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python**: 3.8 或更高版本
- **Git**: 用於克隆代碼庫

## 🚀 快速安裝

### 1. 克隆項目
```bash
git clone https://github.com/your-username/reddit-research.git
cd reddit-research
```

### 2. 創建虛擬環境
```bash
# 使用 venv
python -m venv venv

# 激活虛擬環境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. 安裝依賴
```bash
pip install -r requirements.txt
```

### 4. 配置環境變量
創建 `.env` 文件：
```bash
cp .env.example .env
```

編輯 `.env` 文件：
```env
# Reddit API 配置 (必需)
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=your_app_name/1.0

# OpenAI API 配置 (可選，用於高級分析)
OPENAI_API_KEY=your_openai_api_key

# 數據庫配置
DATABASE_PATH=data/reddit_monitor.db

# 報告配置
REPORTS_DIR=reports
```

### 5. 初始化系統
```bash
python main.py
```

## 🔑 Reddit API 設置

### 1. 創建 Reddit 應用

1. 訪問 [Reddit App Preferences](https://www.reddit.com/prefs/apps)
2. 點擊 "Create App" 或 "Create Another App"
3. 填寫應用信息：
   - **Name**: 你的應用名稱
   - **App type**: 選擇 "script"
   - **Description**: 應用描述
   - **About URL**: 留空
   - **Redirect URI**: `http://localhost:8080`

### 2. 獲取 API 憑證

創建應用後，你會看到：
- **Client ID**: 應用名稱下方的字符串
- **Client Secret**: "secret" 標籤後的字符串

### 3. 配置用戶代理

用戶代理格式：`<platform>:<app ID>:<version string> (by /u/<reddit username>)`

示例：`linux:reddit-monitor:v1.0 (by /u/yourusername)`

## 🗄️ 數據庫設置

### 自動初始化
系統首次運行時會自動創建 SQLite 數據庫和所需表結構。

### 手動初始化
如果需要重新初始化數據庫：
```bash
python scripts/setup/setup_database.py
```

### 數據庫位置
默認數據庫文件位置：`data/reddit_monitor.db`

## 📦 依賴包詳解

### 核心依賴
```
praw>=7.7.1              # Reddit API 客戶端
requests>=2.31.0         # HTTP 請求庫
schedule>=1.2.0          # 任務調度
python-dotenv>=1.0.0     # 環境變量管理
```

### 可選依賴
```
openai>=1.0.0           # OpenAI API (高級情緒分析)
pandas>=2.0.0           # 數據分析 (可選)
matplotlib>=3.7.0       # 圖表生成 (可選)
```

## 🔧 配置選項

### 環境變量詳解

#### Reddit API 配置
```env
# 必需配置
REDDIT_CLIENT_ID=abc123              # Reddit 應用 Client ID
REDDIT_CLIENT_SECRET=xyz789          # Reddit 應用 Client Secret
REDDIT_USER_AGENT=myapp/1.0          # 用戶代理字符串

# 可選配置
REDDIT_USERNAME=your_username        # Reddit 用戶名 (用於認證)
REDDIT_PASSWORD=your_password        # Reddit 密碼 (用於認證)
```

#### 數據庫配置
```env
DATABASE_PATH=data/reddit_monitor.db # 數據庫文件路徑
DATABASE_BACKUP_DIR=backups          # 備份目錄
DATABASE_BACKUP_INTERVAL=24          # 備份間隔 (小時)
```

#### 收集配置
```env
POSTS_PER_SUBREDDIT=100              # 每個 subreddit 收集帖子數
COMMENTS_PER_POST=50                 # 每個帖子收集評論數
COLLECTION_INTERVAL=3600             # 收集間隔 (秒)
MAX_COLLECTION_THREADS=4             # 最大收集線程數
```

#### 分析配置
```env
SENTIMENT_CONFIDENCE_THRESHOLD=0.6   # 情緒分析信心度閾值
ENTITY_CONFIDENCE_THRESHOLD=0.5      # 實體識別信心度閾值
ENABLE_LLM_ANALYSIS=false           # 是否啟用 LLM 分析
```

#### 報告配置
```env
REPORTS_DIR=reports                  # 報告輸出目錄
REPORT_RETENTION_DAYS=30            # 報告保留天數
AUTO_REPORT_GENERATION=true         # 自動生成報告
REPORT_EMAIL_NOTIFICATIONS=false    # 郵件通知
```

## 🧪 驗證安裝

### 1. 測試 Reddit 連接
```bash
python scripts/setup/test_reddit_connection.py
```

### 2. 測試數據庫
```bash
python scripts/setup/test_database.py
```

### 3. 測試完整系統
```bash
python scripts/setup/test_complete_system.py
```

### 4. 運行分析器測試
```bash
python scripts/setup/test_analyzers.py
```

## 🚦 啟動系統

### 1. 一次性數據收集
```bash
python scripts/data/collect_large_dataset.py
```

### 2. 持續監控
```bash
python scripts/data/continuous_monitor.py
```

### 3. 生成報告
```bash
python scripts/maintenance/generate_report_now.py
```

## 🐛 常見問題

### Reddit API 問題

**問題**: `prawcore.exceptions.ResponseException: received 401 HTTP response`
**解決方案**: 
1. 檢查 Client ID 和 Client Secret 是否正確
2. 確認用戶代理格式正確
3. 檢查網絡連接

**問題**: `prawcore.exceptions.TooManyRequests`
**解決方案**:
1. 降低收集頻率
2. 增加請求間隔
3. 檢查是否超出 API 限制

### 數據庫問題

**問題**: `sqlite3.OperationalError: database is locked`
**解決方案**:
1. 確保沒有其他進程使用數據庫
2. 檢查文件權限
3. 重啟應用程序

**問題**: `sqlite3.OperationalError: no such table`
**解決方案**:
1. 重新初始化數據庫
2. 檢查數據庫文件完整性
3. 運行數據庫設置腳本

### 依賴問題

**問題**: `ModuleNotFoundError: No module named 'praw'`
**解決方案**:
1. 確認虛擬環境已激活
2. 重新安裝依賴：`pip install -r requirements.txt`
3. 檢查 Python 版本兼容性

### 權限問題

**問題**: `PermissionError: [Errno 13] Permission denied`
**解決方案**:
1. 檢查文件和目錄權限
2. 使用管理員權限運行 (Windows)
3. 使用 `sudo` 運行 (Linux/macOS)

## 🔄 更新系統

### 1. 備份數據
```bash
cp data/reddit_monitor.db data/reddit_monitor_backup.db
```

### 2. 拉取最新代碼
```bash
git pull origin main
```

### 3. 更新依賴
```bash
pip install -r requirements.txt --upgrade
```

### 4. 運行遷移 (如果需要)
```bash
python scripts/maintenance/migrate_database.py
```

## 🗑️ 卸載

### 1. 停止所有進程
```bash
# 停止持續監控
pkill -f continuous_monitor.py
```

### 2. 備份重要數據
```bash
cp data/reddit_monitor.db ~/reddit_backup.db
cp -r reports ~/reddit_reports_backup
```

### 3. 刪除虛擬環境
```bash
deactivate
rm -rf venv
```

### 4. 刪除項目文件
```bash
cd ..
rm -rf reddit-research
```

## 📞 獲取幫助

### 文檔資源
- [系統架構](ARCHITECTURE.md)
- [API 文檔](API.md)
- [使用指南](USAGE.md)

### 社區支持
- GitHub Issues: 報告 bug 和功能請求
- 討論區: 技術討論和經驗分享

### 聯繫方式
- Email: <EMAIL>
- Discord: [邀請鏈接]

## 📝 下一步

安裝完成後，建議：

1. **閱讀使用指南**: 了解系統功能和操作方法
2. **運行測試收集**: 收集少量數據測試系統
3. **配置調度**: 設置自動化數據收集和分析
4. **自定義配置**: 根據需求調整參數
5. **監控日誌**: 觀察系統運行狀況

祝你使用愉快！🎉
