# API 文檔

## 📋 概述

本文檔描述了 Reddit 股票加密貨幣監控系統的內部 API 和數據接口。

## 🗄️ 數據庫 API

### DatabaseManager 類

#### 連接管理
```python
from src.storage.database import DatabaseManager

db = DatabaseManager()
conn = db.models.get_connection()
```

#### 基本查詢方法

##### 獲取 Subreddits
```python
# 獲取所有活躍的 subreddits
subreddits = db.get_subreddits_by_category("stock")
subreddits = db.get_subreddits_by_category("crypto")

# 返回格式
[
    {
        "id": 1,
        "name": "wallstreetbets",
        "category": "stock",
        "subscribers": 15000000,
        "is_active": True
    }
]
```

##### 獲取帖子數據
```python
# 獲取最近的帖子
posts = db.get_recent_posts(hours=24, limit=100)

# 返回格式
[
    {
        "id": "abc123",
        "subreddit_id": 1,
        "title": "TSLA to the moon!",
        "content": "Tesla stock analysis...",
        "author": "username",
        "score": 1500,
        "upvote_ratio": 0.85,
        "num_comments": 200,
        "created_utc": "2025-07-31 12:00:00"
    }
]
```

##### 獲取情緒統計
```python
# 獲取情緒摘要
sentiment_stats = db.get_sentiment_summary(hours=24)

# 返回格式
{
    "total_analyzed": 1000,
    "positive_count": 600,
    "negative_count": 200,
    "neutral_count": 200,
    "avg_sentiment_score": 0.35
}
```

## 🔍 分析器 API

### EntityExtractor 類

#### 實體提取
```python
from src.analyzers.entity_extractor import EntityExtractor

extractor = EntityExtractor()

# 提取實體
entities = extractor.extract_entities(
    content="I'm bullish on AAPL and BTC",
    source_id="post_123",
    source_type="post"
)

# 返回格式
[
    {
        "symbol": "AAPL",
        "entity_type": "stock",
        "confidence": 0.95,
        "context": "I'm bullish on AAPL",
        "validation_reason": "Confirmed by stock API"
    },
    {
        "symbol": "BTC",
        "entity_type": "crypto",
        "confidence": 0.98,
        "context": "AAPL and BTC",
        "validation_reason": "Confirmed by crypto API"
    }
]
```

#### 批量提取
```python
# 從最近帖子提取實體
count = extractor.extract_from_recent_posts(hours=6)

# 從最近評論提取實體
count = extractor.extract_from_recent_comments(hours=6)
```

### SentimentAnalyzer 類

#### 情緒分析
```python
from src.analyzers.sentiment_analyzer import SentimentAnalyzer

analyzer = SentimentAnalyzer()

# 分析單個文本
result = analyzer.analyze_sentiment(
    content="TSLA is going to the moon! 🚀",
    entity_context="TSLA"
)

# 返回格式
{
    "sentiment": "positive",
    "confidence": 0.87,
    "score": 0.65,
    "reasoning": "Positive keywords: moon, rocket emoji"
}
```

#### 批量分析
```python
# 分析最近帖子
posts_analyzed = analyzer.analyze_recent_posts(hours=6)

# 分析最近評論
comments_analyzed = analyzer.analyze_recent_comments(hours=6)
```

### TrendAnalyzer 類

#### 趨勢計算
```python
from src.analyzers.trend_analyzer import TrendAnalyzer

analyzer = TrendAnalyzer()

# 計算所有趨勢
results = analyzer.calculate_all_trends()

# 返回格式
{
    "1h": 25,    # 1小時內計算的實體數量
    "6h": 150,   # 6小時內計算的實體數量
    "24h": 200,  # 24小時內計算的實體數量
    "7d": 300    # 7天內計算的實體數量
}
```

#### 獲取排行榜
```python
# 獲取熱門趨勢
top_stocks = analyzer.get_top_trending("stock", "6h", 10)
top_cryptos = analyzer.get_top_trending("crypto", "6h", 10)

# 返回格式
[
    {
        "entity_type": "stock",
        "symbol": "AAPL",
        "mention_count": 150,
        "positive_sentiment_count": 90,
        "negative_sentiment_count": 30,
        "neutral_sentiment_count": 30,
        "avg_sentiment_score": 0.45,
        "trend_score": 8.75,
        "calculated_at": "2025-07-31 22:00:00"
    }
]
```

#### 特殊排名
```python
# 突然爆紅
momentum_stocks = analyzer.get_trending_by_momentum("stock", "6h", 10)

# 小眾熱門
niche_stocks = analyzer.get_niche_favorites("stock", "6h", 10)

# Meme 潛力
meme_stocks = analyzer.get_meme_potential("stock", "6h", 10)

# 小市值寶石
gem_stocks = analyzer.get_small_cap_gems("stock", "6h", 10)
```

#### 市場概覽
```python
# 獲取市場概覽
overview = analyzer.get_market_overview("6h")

# 返回格式
{
    "sentiment_overview": {
        "overall_sentiment": "positive",
        "stock_sentiment": 0.25,
        "crypto_sentiment": 0.35
    },
    "activity_stats": {
        "total_posts": 500,
        "total_comments": 5000,
        "entities_extracted": 200,
        "sentiments_analyzed": 1000
    },
    "top_mentions": [...],
    "trending_topics": [...]
}
```

## 📊 報告生成 API

### ReportGenerator 類

#### 生成報告
```python
from src.reporters.report_generator import ReportGenerator

generator = ReportGenerator()

# 生成每小時報告
hourly_report = generator.generate_hourly_report()

# 生成每日報告
daily_report = generator.generate_daily_report()

# 生成每週報告
weekly_report = generator.generate_weekly_report()

# 返回報告文件路徑
# "reports/hourly_report_20250731_2200.html"
```

#### 報告數據收集
```python
# 收集報告數據
report_data = generator._collect_report_data("6h")

# 返回完整的報告數據結構（見 ARCHITECTURE.md）
```

## 🔄 收集器 API

### RedditCollector 類

#### 基本收集
```python
from src.collectors.reddit_collector import RedditCollector

collector = RedditCollector()

# 收集單個 subreddit
posts = collector.collect_subreddit_posts(
    subreddit_name="wallstreetbets",
    limit=50,
    post_types=["hot", "new"]
)

# 收集帖子評論
comments = collector.collect_post_comments(
    post_id="abc123",
    limit=20
)
```

#### 批量收集
```python
# 收集所有 subreddits
results = collector.collect_all_subreddits()

# 批量收集大量數據
bulk_results = collector.bulk_collect_data(
    target_posts=1000,
    max_subreddits=10
)

# 返回格式
{
    "posts": 850,
    "comments": 8500,
    "errors": 2,
    "subreddits_processed": 10
}
```

#### 多樣化收集
```python
# 收集多樣化帖子
diverse_posts = collector.collect_diverse_posts(
    subreddit_name="stocks",
    total_limit=200
)
```

## 🧠 智能驗證 API

### SmartSymbolValidator 類

#### 符號驗證
```python
from src.analyzers.smart_symbol_validator import SmartSymbolValidator

validator = SmartSymbolValidator()

# 驗證單個符號
is_valid, confidence, reason = validator.validate_symbol(
    symbol="AAPL",
    context="I'm buying AAPL stock",
    entity_type="stock"
)

# 返回格式
# (True, 0.95, "Confirmed by stock API")
```

#### 緩存統計
```python
# 獲取緩存統計
stats = validator.get_cache_stats()

# 返回格式
{
    "cache_size": 150,
    "api_calls": {
        "alpha_vantage": 25,
        "coingecko": 30
    },
    "last_reset": {
        "alpha_vantage": 1627776000,
        "coingecko": 1627776000
    }
}
```

## 📅 調度器 API

### TaskScheduler 類

#### 調度管理
```python
from src.schedulers.task_scheduler import TaskScheduler

scheduler = TaskScheduler()

# 啟動調度器
scheduler.start()

# 停止調度器
scheduler.stop()

# 檢查狀態
is_running = scheduler.is_running()
```

#### 手動執行任務
```python
# 手動執行收集任務
scheduler.collect_data()

# 手動執行分析任務
scheduler.analyze_data()

# 手動生成報告
scheduler.generate_reports()
```

## 🔧 配置 API

### Config 類

#### 配置管理
```python
from src.utils.config import Config

config = Config()

# 驗證配置
is_valid = config.validate()

# 獲取配置值
posts_per_subreddit = config.POSTS_PER_SUBREDDIT
comments_per_post = config.COMMENTS_PER_POST
```

#### 環境變量
```python
# 必需的環境變量
REDDIT_CLIENT_ID = config.REDDIT_CLIENT_ID
REDDIT_CLIENT_SECRET = config.REDDIT_CLIENT_SECRET
REDDIT_USER_AGENT = config.REDDIT_USER_AGENT

# 可選的環境變量
OPENAI_API_KEY = config.OPENAI_API_KEY
DATABASE_PATH = config.DATABASE_PATH
```

## 📝 數據模型

### 實體數據模型
```python
Entity = {
    "id": int,
    "symbol": str,
    "entity_type": str,  # "stock" | "crypto"
    "source_type": str,  # "post" | "comment"
    "source_id": str,
    "confidence": float,  # 0.0 - 1.0
    "context": str,
    "extracted_at": datetime
}
```

### 情緒數據模型
```python
Sentiment = {
    "id": int,
    "source_type": str,  # "post" | "comment"
    "source_id": str,
    "sentiment": str,    # "positive" | "negative" | "neutral"
    "confidence": float, # 0.0 - 1.0
    "score": float,      # -1.0 to 1.0
    "analyzed_at": datetime
}
```

### 趨勢數據模型
```python
Trend = {
    "id": int,
    "entity_type": str,  # "stock" | "crypto"
    "symbol": str,
    "time_period": str,  # "1h" | "6h" | "24h" | "7d"
    "mention_count": int,
    "positive_sentiment_count": int,
    "negative_sentiment_count": int,
    "neutral_sentiment_count": int,
    "avg_sentiment_score": float,
    "trend_score": float,
    "calculated_at": datetime
}
```

## 🚨 錯誤處理

### 常見錯誤代碼
- `REDDIT_API_ERROR`: Reddit API 連接失敗
- `DATABASE_ERROR`: 數據庫操作失敗
- `VALIDATION_ERROR`: 數據驗證失敗
- `ANALYSIS_ERROR`: 分析過程出錯
- `REPORT_ERROR`: 報告生成失敗

### 錯誤處理示例
```python
try:
    entities = extractor.extract_entities(content, source_id, source_type)
except ValidationError as e:
    logger.error(f"數據驗證失敗: {e}")
except DatabaseError as e:
    logger.error(f"數據庫錯誤: {e}")
except Exception as e:
    logger.error(f"未知錯誤: {e}")
```

## 📊 性能指標

### API 響應時間
- 實體提取：< 100ms
- 情緒分析：< 50ms
- 趨勢計算：< 500ms
- 報告生成：< 2s

### 吞吐量
- 帖子處理：100 帖子/分鐘
- 評論處理：1000 評論/分鐘
- 實體提取：500 實體/分鐘
- 情緒分析：1000 分析/分鐘

這些 API 提供了完整的系統功能訪問，支持自定義開發和集成。
