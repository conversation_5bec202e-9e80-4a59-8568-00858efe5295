# Reddit 股票加密貨幣監控系統

一個智能的 Reddit 監控系統，用於發現股票和加密貨幣的趨勢、情緒分析和潛在寶石項目。

## 🎯 系統概述

本系統通過監控 Reddit 上的金融相關討論，自動識別熱門股票和加密貨幣，分析市場情緒，並發現潛在的投資機會。

### 核心功能

- 📊 **實時數據收集**：從多個 Reddit 群組收集帖子和評論
- 🎭 **情緒分析**：分析用戶對股票/加密貨幣的情緒傾向
- 🔍 **智能實體識別**：準確識別股票代碼和加密貨幣符號
- 📈 **趨勢計算**：計算多時間段的趨勢分數
- 💎 **寶石發現**：識別小市值潛力項目
- 📊 **報告生成**：生成詳細的 HTML 和 JSON 報告

## 🏗️ 系統架構

```
reddit-research/
├── src/                    # 核心源代碼
│   ├── analyzers/         # 分析器模塊
│   │   ├── entity_extractor.py      # 實體提取器
│   │   ├── sentiment_analyzer.py    # 情緒分析器
│   │   ├── trend_analyzer.py        # 趨勢分析器
│   │   └── smart_symbol_validator.py # 智能符號驗證器
│   ├── collectors/        # 數據收集器
│   │   └── reddit_collector.py      # Reddit 數據收集器
│   ├── reporters/         # 報告生成器
│   │   └── report_generator.py      # 報告生成器
│   ├── storage/          # 數據存儲
│   │   ├── database.py              # 數據庫管理器
│   │   └── models.py                # 數據模型
│   ├── schedulers/       # 調度器
│   │   └── task_scheduler.py        # 任務調度器
│   └── utils/            # 工具類
│       └── config.py                # 配置管理
├── scripts/              # 實用腳本
│   ├── setup/           # 安裝和設置腳本
│   ├── data/            # 數據相關腳本
│   ├── analysis/        # 分析腳本
│   └── maintenance/     # 維護腳本
├── docs/                # 文檔
├── reports/             # 生成的報告
├── data/               # 數據文件
└── tests/              # 測試文件
```

## 📋 系統要求

### 環境要求
- Python 3.8+
- SQLite 3
- 網絡連接（用於 Reddit API）

### 依賴包
```
praw>=7.7.1
requests>=2.31.0
schedule>=1.2.0
python-dotenv>=1.0.0
```

## 🚀 安裝指南

### 1. 克隆項目
```bash
git clone <repository-url>
cd reddit-research
```

### 2. 安裝依賴
```bash
pip install -r requirements.txt
```

### 3. 配置環境變量
創建 `.env` 文件：
```env
# Reddit API 配置
REDDIT_CLIENT_ID=your_client_id
REDDIT_CLIENT_SECRET=your_client_secret
REDDIT_USER_AGENT=your_user_agent

# 可選：OpenAI API（用於高級情緒分析）
OPENAI_API_KEY=your_openai_api_key

# 數據庫配置
DATABASE_PATH=data/reddit_monitor.db

# 報告配置
REPORTS_DIR=reports
```

### 4. 初始化數據庫
```bash
python main.py
```

## 🎮 使用指南

### 快速開始

1. **啟動持續監控**：
```bash
python scripts/data/continuous_monitor.py
```

2. **收集大量數據**：
```bash
python scripts/data/collect_large_dataset.py
```

3. **生成報告**：
```bash
python scripts/maintenance/generate_report_now.py
```

### 主要腳本說明

#### 數據收集腳本
- `scripts/data/continuous_monitor.py`：24/7 持續監控系統
- `scripts/data/collect_large_dataset.py`：批量收集大量數據
- `scripts/data/check_database_stats.py`：檢查數據庫統計

#### 分析腳本
- `scripts/analysis/debug_trends.py`：調試趨勢計算
- `scripts/analysis/recalculate_trends.py`：重新計算趨勢
- `scripts/analysis/test_smart_validation.py`：測試智能驗證

#### 維護腳本
- `scripts/maintenance/generate_report_now.py`：立即生成報告
- `scripts/maintenance/clean_invalid_entities.py`：清理無效實體

## 🔧 核心模塊詳解

### 1. 數據收集器 (Reddit Collector)

**功能**：從 Reddit 收集帖子和評論數據

**關鍵參數**：
- `POSTS_PER_SUBREDDIT`：每個 subreddit 收集的帖子數量（默認：100）
- `COMMENTS_PER_POST`：每個帖子收集的評論數量（默認：50）
- `COLLECTION_INTERVAL`：收集間隔秒數（默認：3600）

**支持的 Subreddits**：
- 股票：wallstreetbets, stocks, investing, StockMarket 等
- 加密貨幣：CryptoCurrency, Bitcoin, ethereum, dogecoin 等

### 2. 實體提取器 (Entity Extractor)

**功能**：從文本中識別股票代碼和加密貨幣符號

**智能驗證機制**：
- 格式驗證：檢查符號格式是否合理
- 黑名單過濾：排除常見英文單詞
- 上下文分析：分析周圍文字的金融相關性
- API 驗證：調用外部 API 確認符號真實性

**關鍵參數**：
- `confidence_threshold`：實體識別信心度閾值（默認：0.5）
- `context_window`：上下文分析窗口大小（默認：20字符）

### 3. 情緒分析器 (Sentiment Analyzer)

**功能**：分析用戶對股票/加密貨幣的情緒傾向

**分析方法**：
- 關鍵詞匹配：基於預定義的正面/負面詞彙
- 上下文分析：考慮詞彙在特定語境中的含義
- 強度計算：計算情緒的強烈程度

**情緒分類**：
- Positive（正面）：看漲、樂觀
- Negative（負面）：看跌、悲觀
- Neutral（中性）：客觀、無明確傾向

### 4. 趨勢分析器 (Trend Analyzer)

**功能**：計算股票和加密貨幣的趨勢分數

**計算公式**：
```
趨勢分數 = (提及次數 × 0.4) + (正面情緒比例 × 0.3) + (平均情緒分數 × 0.3)
```

**時間段**：
- 1h：最近1小時
- 6h：最近6小時
- 24h：最近24小時
- 7d：最近7天

**排名類型**：
- **熱門趨勢**：按趨勢分數排序
- **突然爆紅**：提及次數突然增加
- **小眾熱門**：高情緒但低提及次數
- **Meme 潛力**：具有 meme 特徵的項目
- **小市值寶石**：潛在的小市值機會

## 📊 報告系統

### 報告類型

1. **每小時報告**：最新6小時的數據分析
2. **每日報告**：最近24小時的詳細分析
3. **每週報告**：7天的趨勢總結

### 報告內容

- **市場概覽**：整體市場情緒和活動統計
- **熱門排行**：各類排名的前15名
- **趨勢分析**：上升/下降趨勢
- **異常檢測**：突然的趨勢變化
- **活躍群組**：最活躍的 subreddit 統計

### 報告格式

- **HTML**：可視化的網頁報告
- **JSON**：結構化數據，便於程序處理

## ⚙️ 配置參數

### 數據收集配置
```python
POSTS_PER_SUBREDDIT = 100        # 每個 subreddit 收集的帖子數量
COMMENTS_PER_POST = 50           # 每個帖子收集的評論數量
COLLECTION_INTERVAL = 3600       # 收集間隔（秒）
BULK_POSTS_PER_SUBREDDIT = 200   # 批量收集時的帖子數量
TARGET_TOTAL_POSTS = 1000        # 目標總帖子數量
```

### 分析配置
```python
SENTIMENT_CONFIDENCE_THRESHOLD = 0.6    # 情緒分析信心度閾值
ENTITY_CONFIDENCE_THRESHOLD = 0.5       # 實體識別信心度閾值
TREND_CALCULATION_INTERVALS = [1, 6, 24, 168]  # 趨勢計算時間間隔（小時）
```

### 報告配置
```python
REPORTS_DIR = "reports"                  # 報告輸出目錄
REPORT_RETENTION_DAYS = 30              # 報告保留天數
AUTO_REPORT_GENERATION = True           # 自動生成報告
```

## 🔄 調度系統

### 自動任務

- **每15分鐘**：快速收集新數據
- **每30分鐘**：分析新收集的數據
- **每1小時**：生成報告
- **每6小時**：大量數據收集

### 手動任務

- 重新計算趨勢
- 清理無效數據
- 生成特定時間段報告
- 數據庫維護

## 🛠️ 故障排除

### 常見問題

1. **Reddit API 連接失敗**
   - 檢查 `.env` 文件中的 API 配置
   - 確認網絡連接正常
   - 檢查 API 使用限制

2. **數據庫錯誤**
   - 檢查數據庫文件權限
   - 確認磁盤空間充足
   - 重新初始化數據庫

3. **報告生成失敗**
   - 檢查報告目錄權限
   - 確認有足夠的數據
   - 檢查模板文件完整性

### 日誌系統

系統會在以下位置生成日誌：
- `app.log`：主要應用日誌
- `logs/`：詳細的模塊日誌

## 📈 性能優化

### 數據庫優化
- 定期清理舊數據
- 建立適當的索引
- 使用批量插入

### API 優化
- 實現請求限制
- 使用緩存機制
- 錯誤重試機制

### 內存優化
- 分批處理大量數據
- 及時釋放資源
- 使用生成器處理大數據集

## 🔒 安全考慮

- API 密鑰安全存儲
- 數據庫訪問控制
- 輸入數據驗證
- 錯誤信息過濾

## 📚 詳細文檔

- [📖 安裝指南](INSTALLATION.md) - 詳細的安裝和配置步驟
- [🏗️ 系統架構](ARCHITECTURE.md) - 深入的架構設計文檔
- [🔌 API 文檔](API.md) - 完整的 API 參考
- [📋 使用指南](USAGE.md) - 詳細的使用說明和最佳實踐

## 📞 支持與貢獻

### 問題報告
如果遇到問題，請提供：
- 錯誤信息
- 系統環境
- 重現步驟
- 相關日誌

### 貢獻指南
1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 創建 Pull Request

## 📄 許可證

本項目採用 MIT 許可證。詳見 LICENSE 文件。
