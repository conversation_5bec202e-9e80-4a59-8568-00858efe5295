# 腳本索引

本文檔列出了所有可用的腳本及其用途。

## 📁 腳本組織結構

```
scripts/
├── setup/              # 安裝和設置腳本
├── data/               # 數據收集腳本
├── analysis/           # 分析工具腳本
└── maintenance/        # 維護工具腳本
```

## 🔧 設置腳本 (scripts/setup/)

### start_monitoring.py
**用途**: 啟動監控系統的便捷腳本
**使用方法**:
```bash
python scripts/setup/start_monitoring.py
```
**功能**:
- 檢查系統配置
- 初始化數據庫
- 啟動數據收集和分析

### test_reddit_connection.py
**用途**: 測試 Reddit API 連接
**使用方法**:
```bash
python scripts/setup/test_reddit_connection.py
```
**功能**:
- 驗證 Reddit API 憑證
- 測試 API 連接速度
- 檢查 API 限制狀態

### test_complete_system.py
**用途**: 完整的系統測試
**使用方法**:
```bash
python scripts/setup/test_complete_system.py
```
**功能**:
- 測試所有模塊
- 驗證數據流
- 生成測試報告

### test_analyzers.py
**用途**: 測試分析器模塊
**使用方法**:
```bash
python scripts/setup/test_analyzers.py
```
**功能**:
- 測試情緒分析器
- 測試實體提取器
- 測試趨勢分析器

### test_new_rankings.py
**用途**: 測試新的排名算法
**使用方法**:
```bash
python scripts/setup/test_new_rankings.py
```
**功能**:
- 測試各種排名算法
- 驗證排名結果
- 性能基準測試

## 📊 數據收集腳本 (scripts/data/)

### collect_large_dataset.py
**用途**: 批量收集大量數據
**使用方法**:
```bash
python scripts/data/collect_large_dataset.py
```
**選項**:
- 選項 1: 快速收集 (500 個帖子)
- 選項 2: 標準收集 (1000 個帖子)
- 選項 3: 大量收集 (2000 個帖子)
- 選項 4: 針對性收集 (高質量 subreddits)

**功能**:
- 從多個 subreddit 收集帖子
- 收集評論數據
- 自動進行情緒分析和實體提取
- 生成收集統計報告

### continuous_monitor.py
**用途**: 24/7 持續監控系統
**使用方法**:
```bash
python scripts/data/continuous_monitor.py
```
**調度計劃**:
- 每15分鐘: 快速收集新數據
- 每30分鐘: 分析新收集的數據
- 每1小時: 生成報告
- 每6小時: 大量數據收集

**功能**:
- 持續數據收集
- 自動錯誤恢復
- 實時狀態監控
- 智能調度管理

### check_database_stats.py
**用途**: 檢查數據庫統計信息
**使用方法**:
```bash
python scripts/data/check_database_stats.py
```
**輸出信息**:
- 總體數據統計
- 時間分布統計
- 最新數據時間
- 熱門實體統計
- Subreddit 統計

## 🔍 分析腳本 (scripts/analysis/)

### debug_trends.py
**用途**: 調試趨勢計算系統
**使用方法**:
```bash
python scripts/analysis/debug_trends.py
```
**功能**:
- 檢查趨勢表結構
- 統計趨勢數據
- 測試趨勢查詢
- 驗證計算邏輯

### recalculate_trends.py
**用途**: 重新計算所有趨勢數據
**使用方法**:
```bash
python scripts/analysis/recalculate_trends.py
```
**功能**:
- 清除舊趨勢數據
- 重新計算所有時間段
- 生成新報告
- 驗證計算結果

### test_smart_validation.py
**用途**: 測試智能符號驗證系統
**使用方法**:
```bash
python scripts/analysis/test_smart_validation.py
```
**測試內容**:
- 真實股票/加密貨幣識別
- 錯誤詞彙過濾
- 上下文分析準確性
- 整體準確率統計

## 🛠️ 維護腳本 (scripts/maintenance/)

### generate_report_now.py
**用途**: 立即生成最新報告
**使用方法**:
```bash
python scripts/maintenance/generate_report_now.py
```
**功能**:
- 跳過調度，立即生成報告
- 使用最新數據
- 生成 HTML 和 JSON 格式
- 更新最新報告鏈接

### clean_invalid_entities.py
**用途**: 清理無效的實體數據
**使用方法**:
```bash
python scripts/maintenance/clean_invalid_entities.py
```
**功能**:
- 使用智能驗證器檢查所有實體
- 刪除信心度低的實體
- 清理相關趨勢數據
- 重新計算趨勢

## 🚀 主程序

### main.py
**用途**: 系統主入口點
**使用方法**:
```bash
python main.py
```
**功能**:
- 初始化系統
- 設置數據庫
- 啟動調度器
- 開始數據收集

## 📋 使用建議

### 首次使用流程
1. 運行 `scripts/setup/test_reddit_connection.py` 測試 API
2. 運行 `scripts/setup/test_complete_system.py` 驗證系統
3. 運行 `scripts/data/collect_large_dataset.py` 收集初始數據
4. 運行 `scripts/maintenance/generate_report_now.py` 生成首個報告
5. 啟動 `scripts/data/continuous_monitor.py` 持續監控

### 日常維護流程
1. 每天檢查 `scripts/data/check_database_stats.py`
2. 每週運行 `scripts/maintenance/clean_invalid_entities.py`
3. 定期運行 `scripts/analysis/recalculate_trends.py`
4. 監控 `scripts/data/continuous_monitor.py` 運行狀態

### 故障排除流程
1. 運行相關測試腳本診斷問題
2. 檢查日誌文件
3. 重新計算趨勢數據
4. 清理無效數據
5. 重啟監控系統

## ⚙️ 腳本參數

### 環境變量
所有腳本都會讀取 `.env` 文件中的配置：
- `REDDIT_CLIENT_ID`: Reddit API 客戶端 ID
- `REDDIT_CLIENT_SECRET`: Reddit API 客戶端密鑰
- `REDDIT_USER_AGENT`: 用戶代理字符串
- `DATABASE_PATH`: 數據庫文件路徑
- `REPORTS_DIR`: 報告輸出目錄

### 命令行參數
部分腳本支持命令行參數：

```bash
# 指定收集數量
python scripts/data/collect_large_dataset.py --target-posts 1500

# 指定時間範圍
python scripts/analysis/recalculate_trends.py --hours 48

# 指定輸出格式
python scripts/maintenance/generate_report_now.py --format json
```

## 🔧 自定義腳本

### 創建新腳本
1. 在適當的子目錄中創建腳本
2. 添加標準的導入和配置
3. 實現主要功能
4. 添加錯誤處理和日誌
5. 更新本文檔

### 腳本模板
```python
#!/usr/bin/env python3
"""
腳本描述
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.utils.config import Config

def main():
    """主函數"""
    print("🚀 腳本開始...")
    
    try:
        # 驗證配置
        config = Config()
        if not config.validate():
            print("❌ 配置驗證失敗")
            return False
        
        # 主要邏輯
        # ...
        
        print("✅ 腳本完成")
        return True
        
    except Exception as e:
        print(f"❌ 腳本失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

## 📊 性能監控

### 腳本執行時間
- 數據收集腳本: 30-60 分鐘
- 分析腳本: 5-15 分鐘
- 維護腳本: 1-5 分鐘
- 測試腳本: 1-3 分鐘

### 資源使用
- 內存使用: 通常 < 1GB
- CPU 使用: 中等負載
- 網絡使用: 取決於數據收集量
- 磁盤使用: 隨數據增長

## 🚨 注意事項

### API 限制
- Reddit API 有速率限制
- 避免並行運行多個收集腳本
- 監控 API 使用量

### 數據完整性
- 定期備份數據庫
- 驗證數據質量
- 清理無效數據

### 系統穩定性
- 監控長期運行的腳本
- 實現錯誤恢復機制
- 定期重啟服務

這些腳本提供了完整的系統管理和維護功能，確保系統的穩定運行和數據質量。
