# 系統架構文檔

## 🏗️ 整體架構

Reddit 股票加密貨幣監控系統採用模塊化設計，分為數據收集、分析處理、存儲管理和報告生成四個主要層次。

```
┌─────────────────────────────────────────────────────────────┐
│                    用戶界面層                                │
├─────────────────────────────────────────────────────────────┤
│  HTML 報告  │  JSON API  │  命令行工具  │  調度系統        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    業務邏輯層                                │
├─────────────────────────────────────────────────────────────┤
│  趨勢分析器  │  情緒分析器  │  實體提取器  │  報告生成器    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    數據收集層                                │
├─────────────────────────────────────────────────────────────┤
│  Reddit 收集器  │  智能驗證器  │  數據清洗器              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    數據存儲層                                │
├─────────────────────────────────────────────────────────────┤
│  SQLite 數據庫  │  文件系統  │  緩存系統                  │
└─────────────────────────────────────────────────────────────┘
```

## 📊 數據流架構

```
Reddit API → 數據收集 → 實體提取 → 情緒分析 → 趨勢計算 → 報告生成
     ↓           ↓           ↓           ↓           ↓           ↓
  原始數據    結構化數據    實體數據    情緒數據    趨勢數據    最終報告
```

## 🗄️ 數據庫設計

### 核心表結構

#### 1. subreddits 表
```sql
CREATE TABLE subreddits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    category TEXT NOT NULL,
    subscribers INTEGER,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. posts 表
```sql
CREATE TABLE posts (
    id TEXT PRIMARY KEY,
    subreddit_id INTEGER,
    title TEXT NOT NULL,
    content TEXT,
    author TEXT,
    score INTEGER,
    upvote_ratio REAL,
    num_comments INTEGER,
    url TEXT,
    created_utc TIMESTAMP,
    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subreddit_id) REFERENCES subreddits (id)
);
```

#### 3. comments 表
```sql
CREATE TABLE comments (
    id TEXT PRIMARY KEY,
    post_id TEXT,
    parent_id TEXT,
    content TEXT NOT NULL,
    author TEXT,
    score INTEGER,
    created_utc TIMESTAMP,
    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts (id)
);
```

#### 4. entities 表
```sql
CREATE TABLE entities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    source_type TEXT NOT NULL,
    source_id TEXT NOT NULL,
    confidence REAL,
    context TEXT,
    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. sentiment_analysis 表
```sql
CREATE TABLE sentiment_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_type TEXT NOT NULL,
    source_id TEXT NOT NULL,
    sentiment TEXT NOT NULL,
    confidence REAL,
    score REAL,
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6. trends 表
```sql
CREATE TABLE trends (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type TEXT NOT NULL,
    symbol TEXT NOT NULL,
    time_period TEXT NOT NULL,
    mention_count INTEGER,
    positive_sentiment_count INTEGER,
    negative_sentiment_count INTEGER,
    neutral_sentiment_count INTEGER,
    avg_sentiment_score REAL,
    trend_score REAL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 索引設計

```sql
-- 性能優化索引
CREATE INDEX idx_posts_created_utc ON posts(created_utc);
CREATE INDEX idx_comments_created_utc ON comments(created_utc);
CREATE INDEX idx_entities_symbol_type ON entities(symbol, entity_type);
CREATE INDEX idx_trends_symbol_period ON trends(symbol, time_period);
CREATE INDEX idx_sentiment_source ON sentiment_analysis(source_type, source_id);
```

## 🔧 核心模塊架構

### 1. 數據收集器 (RedditCollector)

```python
class RedditCollector:
    """Reddit 數據收集器"""
    
    def __init__(self):
        self.reddit = praw.Reddit(...)  # Reddit API 客戶端
        self.db = DatabaseManager()     # 數據庫管理器
        
    def collect_subreddit_posts(self, subreddit_name, limit, post_types):
        """收集 subreddit 帖子"""
        # 1. 連接 Reddit API
        # 2. 獲取帖子數據
        # 3. 數據清洗和驗證
        # 4. 存儲到數據庫
        
    def collect_post_comments(self, post_id, limit):
        """收集帖子評論"""
        # 1. 獲取評論樹
        # 2. 遞歸處理嵌套評論
        # 3. 過濾和清洗
        # 4. 批量存儲
```

**關鍵設計決策**：
- 使用 PRAW 庫進行 Reddit API 交互
- 實現速率限制避免 API 封禁
- 支持多種帖子類型（hot, new, top, rising）
- 批量數據庫操作提高性能

### 2. 實體提取器 (EntityExtractor)

```python
class EntityExtractor:
    """智能實體提取器"""
    
    def __init__(self):
        self.smart_validator = SmartSymbolValidator()
        self.patterns = self._compile_patterns()
        
    def extract_entities(self, content, source_id, source_type):
        """提取實體"""
        # 1. 正則表達式初步提取
        # 2. LLM 輔助提取（可選）
        # 3. 智能驗證過濾
        # 4. 去重和排序
        
    def _smart_validate_entities(self, entities, content):
        """智能驗證實體"""
        # 1. 格式驗證
        # 2. 黑名單檢查
        # 3. 上下文分析
        # 4. API 驗證
```

**多層驗證機制**：
1. **格式驗證**：檢查符號是否符合股票/加密貨幣格式
2. **黑名單過濾**：排除常見英文單詞和無關詞彙
3. **上下文分析**：分析周圍文字的金融相關性
4. **API 驗證**：調用外部 API 確認符號真實性
5. **信心度評分**：綜合評估給出最終信心度

### 3. 情緒分析器 (SentimentAnalyzer)

```python
class SentimentAnalyzer:
    """情緒分析器"""
    
    def __init__(self):
        self.positive_keywords = {...}  # 正面關鍵詞
        self.negative_keywords = {...}  # 負面關鍵詞
        self.intensifiers = {...}       # 強度修飾詞
        
    def analyze_sentiment(self, content, entity_context):
        """分析情緒"""
        # 1. 關鍵詞匹配
        # 2. 上下文權重計算
        # 3. 強度修正
        # 4. 綜合評分
```

**情緒計算公式**：
```
情緒分數 = (正面詞權重 - 負面詞權重) × 強度修正 × 上下文權重
最終分數 = tanh(情緒分數)  # 歸一化到 [-1, 1]
```

### 4. 趨勢分析器 (TrendAnalyzer)

```python
class TrendAnalyzer:
    """趨勢分析器"""
    
    def calculate_trend_score(self, symbol, entity_type, time_period):
        """計算趨勢分數"""
        # 1. 統計提及次數
        # 2. 計算情緒分布
        # 3. 應用趨勢公式
        # 4. 時間衰減處理
```

**趨勢分數公式**：
```
趨勢分數 = (
    提及次數 × 0.4 +
    正面情緒比例 × 0.3 +
    平均情緒分數 × 0.3
) × 時間衰減因子
```

**特殊排名算法**：

1. **突然爆紅 (Momentum)**：
```python
momentum_score = (current_mentions - previous_mentions) / previous_mentions
```

2. **小眾熱門 (Niche)**：
```python
niche_score = avg_sentiment_score / log(mention_count + 1)
```

3. **Meme 潛力**：
```python
meme_score = emoji_count + exclamation_count + rocket_count
```

4. **小市值寶石**：
```python
gem_score = sentiment_score × (1 / mention_count) × novelty_factor
```

## 🔄 調度系統架構

### 任務調度器 (TaskScheduler)

```python
class TaskScheduler:
    """任務調度器"""
    
    def __init__(self):
        self.schedule = schedule
        self.running = False
        
    def setup_schedule(self):
        """設置調度任務"""
        # 每15分鐘：快速數據收集
        schedule.every(15).minutes.do(self.quick_collect)
        
        # 每30分鐘：數據分析
        schedule.every(30).minutes.do(self.analyze_data)
        
        # 每1小時：生成報告
        schedule.every().hour.do(self.generate_report)
        
        # 每6小時：大量數據收集
        schedule.every(6).hours.do(self.bulk_collect)
```

### 任務執行流程

```
啟動調度器
    ↓
檢查待執行任務
    ↓
執行任務 → 記錄日誌 → 錯誤處理
    ↓
更新下次執行時間
    ↓
等待下一個檢查週期
```

## 📊 報告生成架構

### 報告生成器 (ReportGenerator)

```python
class ReportGenerator:
    """報告生成器"""
    
    def generate_hourly_report(self):
        """生成每小時報告"""
        # 1. 收集數據
        data = self._collect_report_data("6h")
        
        # 2. 生成 JSON 報告
        self._generate_json_report(data, json_path)
        
        # 3. 生成 HTML 報告
        self._generate_html_report(data, html_path)
        
        # 4. 創建最新報告鏈接
        self._create_latest_links(json_path, html_path)
```

### 報告數據結構

```json
{
  "metadata": {
    "generated_at": "2025-07-31T22:00:00",
    "time_period": "6h",
    "report_type": "hourly"
  },
  "market_overview": {
    "total_mentions": 1234,
    "sentiment_distribution": {...},
    "activity_stats": {...}
  },
  "top_stocks": [...],
  "top_cryptos": [...],
  "momentum_stocks": [...],
  "momentum_cryptos": [...],
  "niche_stocks": [...],
  "niche_cryptos": [...],
  "meme_stocks": [...],
  "meme_cryptos": [...],
  "gem_stocks": [...],
  "gem_cryptos": [...],
  "anomalies": [...],
  "active_subreddits": [...]
}
```

## 🔒 安全架構

### 1. API 安全
- 環境變量存儲敏感信息
- API 密鑰輪換機制
- 請求速率限制
- 錯誤信息過濾

### 2. 數據安全
- 輸入數據驗證
- SQL 注入防護
- 數據庫訪問控制
- 敏感數據脫敏

### 3. 系統安全
- 日誌記錄和監控
- 異常處理機制
- 資源使用限制
- 定期安全更新

## 🚀 性能優化架構

### 1. 數據庫優化
- 索引優化
- 查詢優化
- 連接池管理
- 批量操作

### 2. 內存優化
- 對象池
- 緩存機制
- 垃圾回收優化
- 內存洩漏檢測

### 3. 並發優化
- 異步處理
- 線程池
- 任務隊列
- 負載均衡

## 📈 監控和日誌架構

### 日誌系統
```python
# 日誌配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 監控指標
- API 調用次數和成功率
- 數據庫查詢性能
- 內存和 CPU 使用率
- 錯誤率和響應時間

## 🔧 擴展性設計

### 1. 模塊化設計
- 鬆耦合架構
- 插件式擴展
- 配置驅動
- 接口標準化

### 2. 水平擴展
- 數據庫分片
- 負載均衡
- 分佈式處理
- 微服務架構

### 3. 功能擴展
- 新數據源接入
- 新分析算法
- 新報告格式
- 新通知方式

這個架構設計確保了系統的可維護性、可擴展性和高性能，同時保持了代碼的清晰性和模塊化。
