# 使用指南

## 🎯 快速開始

### 第一次使用

1. **完成安裝**: 按照 [安裝指南](INSTALLATION.md) 完成系統安裝
2. **配置 API**: 設置 Reddit API 憑證
3. **測試連接**: 運行測試腳本確認一切正常
4. **收集數據**: 開始第一次數據收集

### 基本工作流程

```
數據收集 → 實體提取 → 情緒分析 → 趨勢計算 → 報告生成
```

## 📊 數據收集

### 一次性大量收集

適用於初始化系統或需要大量歷史數據時：

```bash
python scripts/data/collect_large_dataset.py
```

**選項說明**：
- **選項 1**: 快速收集 (500 個帖子)
- **選項 2**: 標準收集 (1000 個帖子) - **推薦**
- **選項 3**: 大量收集 (2000 個帖子)
- **選項 4**: 針對性收集 (高質量 subreddits)

**預期結果**：
- 收集時間：30-60 分鐘
- 數據量：1000+ 帖子，10000+ 評論
- 自動進行情緒分析和實體提取

### 持續監控

適用於 24/7 運行，持續收集最新數據：

```bash
python scripts/data/continuous_monitor.py
```

**調度計劃**：
- **每15分鐘**: 快速收集新帖子
- **每30分鐘**: 分析新收集的數據
- **每1小時**: 生成最新報告
- **每6小時**: 大量數據收集

**監控輸出**：
```
💓 [14:30:00] 系統運行中...
📥 [14:30:00] 快速收集...
✅ 快速收集完成: 25 個帖子
🔍 [14:45:00] 開始定期分析...
✅ 分析完成: 情緒分析: 50, 實體提取: 30
```

### 檢查數據統計

隨時查看數據庫中的數據量和質量：

```bash
python scripts/data/check_database_stats.py
```

**輸出示例**：
```
📈 總體統計:
  總帖子數: 1,250
  總評論數: 12,500
  總情緒分析: 800
  總實體提取: 450

🔥 熱門實體:
  BTC (crypto): 45 次提及
  AAPL (stock): 38 次提及
  TSLA (stock): 32 次提及
```

## 🔍 分析功能

### 重新計算趨勢

當添加新數據或調整算法後，重新計算所有趨勢：

```bash
python scripts/analysis/recalculate_trends.py
```

### 調試趨勢計算

檢查趨勢計算是否正常工作：

```bash
python scripts/analysis/debug_trends.py
```

**輸出包括**：
- 趨勢數據統計
- 各時間段的數據分布
- 最新趨勢記錄
- 測試不同時間段的查詢

### 測試智能驗證

驗證實體識別的準確性：

```bash
python scripts/analysis/test_smart_validation.py
```

**測試內容**：
- 真實股票/加密貨幣識別
- 錯誤詞彙過濾
- 上下文分析準確性
- 整體準確率統計

## 📊 報告系統

### 立即生成報告

不等待調度，立即生成最新報告：

```bash
python scripts/maintenance/generate_report_now.py
```

### 報告類型

1. **每小時報告** (`hourly_report_YYYYMMDD_HHMM.html`)
   - 最近6小時的數據
   - 實時趨勢分析
   - 快速市場概覽

2. **每日報告** (`daily_report_YYYYMMDD.html`)
   - 最近24小時的詳細分析
   - 完整的排行榜
   - 趨勢變化分析

3. **每週報告** (`weekly_report_YYYY_WNN.html`)
   - 7天的趨勢總結
   - 長期模式識別
   - 週度對比分析

### 報告內容解讀

#### 統計概覽
```
📊 統計概覽
- 收集帖子: 1,250
- 收集評論: 12,500
- 情緒分析: 800
```

#### 熱門排行
- **📈 熱門股票**: 按趨勢分數排序的股票
- **💰 熱門加密貨幣**: 按趨勢分數排序的加密貨幣
- **🚀 突然爆紅**: 提及次數突然增加的項目
- **💎 小眾熱門**: 高情緒但低提及的潛力項目
- **🎭 Meme 潛力**: 具有 meme 特徵的項目
- **💍 小市值寶石**: 可能的小市值機會

#### 趨勢分數解釋
```
趨勢分數 = (提及次數 × 0.4) + (正面情緒比例 × 0.3) + (平均情緒分數 × 0.3)
```

- **8.0+**: 極度熱門
- **6.0-7.9**: 非常熱門
- **4.0-5.9**: 中等熱門
- **2.0-3.9**: 輕微關注
- **< 2.0**: 很少提及

## 🛠️ 維護功能

### 清理無效實體

移除錯誤識別的實體（如 "PRICE", "VALUE" 等）：

```bash
python scripts/maintenance/clean_invalid_entities.py
```

**清理過程**：
1. 使用智能驗證器檢查所有實體
2. 標記信心度低於閾值的實體
3. 從數據庫中刪除無效實體
4. 重新計算趨勢數據

### 數據庫維護

定期清理舊數據，優化性能：

```bash
# 清理30天前的數據
python scripts/maintenance/cleanup_old_data.py --days 30

# 重建索引
python scripts/maintenance/rebuild_indexes.py

# 數據庫備份
python scripts/maintenance/backup_database.py
```

## ⚙️ 自定義配置

### 調整收集參數

編輯 `src/utils/config.py` 或設置環境變量：

```python
# 每個 subreddit 收集的帖子數量
POSTS_PER_SUBREDDIT = 100

# 每個帖子收集的評論數量
COMMENTS_PER_POST = 50

# 收集間隔（秒）
COLLECTION_INTERVAL = 3600
```

### 調整分析參數

```python
# 情緒分析信心度閾值
SENTIMENT_CONFIDENCE_THRESHOLD = 0.6

# 實體識別信心度閾值
ENTITY_CONFIDENCE_THRESHOLD = 0.5

# 趨勢計算時間間隔
TREND_CALCULATION_INTERVALS = [1, 6, 24, 168]  # 小時
```

### 添加新的 Subreddits

編輯 `src/utils/config.py` 中的 `SUBREDDITS` 配置：

```python
SUBREDDITS = {
    "stock": [
        "wallstreetbets",
        "stocks",
        "investing",
        "your_new_subreddit"  # 添加新的股票相關 subreddit
    ],
    "crypto": [
        "CryptoCurrency",
        "Bitcoin",
        "ethereum",
        "your_crypto_subreddit"  # 添加新的加密貨幣 subreddit
    ]
}
```

## 📈 最佳實踐

### 數據收集最佳實踐

1. **初始收集**: 首次使用時收集大量歷史數據
2. **持續監控**: 設置持續監控保持數據新鮮
3. **定期檢查**: 每天檢查數據統計和系統狀態
4. **錯誤處理**: 監控日誌，及時處理錯誤

### 分析最佳實踐

1. **多時間段分析**: 結合1h, 6h, 24h, 7d的數據
2. **交叉驗證**: 對比不同排名的結果
3. **上下文理解**: 結合市場新聞理解趨勢
4. **風險評估**: 注意小市值項目的風險

### 報告使用最佳實踐

1. **定期查看**: 每天查看最新報告
2. **趨勢對比**: 對比不同時間段的報告
3. **深入分析**: 點擊感興趣的項目查看詳情
4. **記錄發現**: 記錄有價值的發現和投資機會

## 🚨 注意事項

### 投資風險警告

⚠️ **重要提醒**: 本系統僅供參考，不構成投資建議。投資有風險，請謹慎決策。

### 數據準確性

- Reddit 數據可能包含噪音和錯誤信息
- 情緒分析基於關鍵詞，可能不完全準確
- 小市值項目風險較高，需要額外謹慎

### 系統限制

- 依賴 Reddit API，可能受到限制
- 實時性有限，存在數據延遲
- 無法預測市場操縱或突發事件

## 🔧 故障排除

### 常見問題

1. **數據收集停止**
   - 檢查 Reddit API 配置
   - 查看錯誤日誌
   - 重啟持續監控

2. **報告顯示無數據**
   - 確認數據庫中有數據
   - 檢查時間窗口設置
   - 重新計算趨勢

3. **實體識別不準確**
   - 運行智能驗證測試
   - 清理無效實體
   - 調整信心度閾值

### 性能優化

1. **數據庫優化**
   - 定期清理舊數據
   - 重建索引
   - 使用 SSD 存儲

2. **內存優化**
   - 調整批處理大小
   - 定期重啟長期運行的進程
   - 監控內存使用

3. **網絡優化**
   - 使用穩定的網絡連接
   - 調整請求間隔
   - 實現重試機制

## 📞 獲取幫助

如果遇到問題：

1. 查看相關文檔
2. 檢查日誌文件
3. 運行診斷腳本
4. 提交 GitHub Issue

記住：耐心和持續的數據收集是發現真正趨勢的關鍵！🚀
