# 項目總覽

## 🎯 項目簡介

Reddit 股票加密貨幣監控系統是一個智能的金融市場情緒分析工具，通過監控 Reddit 上的討論來發現投資趨勢和機會。

## 🏗️ 完整項目結構

```
reddit-research/
├── 📁 src/                          # 核心源代碼
│   ├── 📁 analyzers/               # 分析器模塊
│   │   ├── entity_extractor.py     # 實體提取器
│   │   ├── sentiment_analyzer.py   # 情緒分析器
│   │   ├── trend_analyzer.py       # 趨勢分析器
│   │   └── smart_symbol_validator.py # 智能符號驗證器
│   ├── 📁 collectors/              # 數據收集器
│   │   └── reddit_collector.py     # Reddit 數據收集器
│   ├── 📁 reporters/               # 報告生成器
│   │   └── report_generator.py     # 報告生成器
│   ├── 📁 storage/                 # 數據存儲
│   │   ├── database.py             # 數據庫管理器
│   │   └── models.py               # 數據模型
│   ├── 📁 schedulers/              # 調度器
│   │   └── task_scheduler.py       # 任務調度器
│   └── 📁 utils/                   # 工具類
│       └── config.py               # 配置管理
├── 📁 scripts/                     # 實用腳本
│   ├── 📁 setup/                   # 安裝和設置腳本
│   │   ├── start_monitoring.py     # 啟動監控
│   │   ├── test_reddit_connection.py # 測試 Reddit 連接
│   │   ├── test_complete_system.py # 完整系統測試
│   │   ├── test_analyzers.py       # 測試分析器
│   │   └── test_new_rankings.py    # 測試新排名
│   ├── 📁 data/                    # 數據相關腳本
│   │   ├── collect_large_dataset.py # 批量數據收集
│   │   ├── continuous_monitor.py   # 持續監控
│   │   └── check_database_stats.py # 數據庫統計
│   ├── 📁 analysis/                # 分析腳本
│   │   ├── debug_trends.py         # 調試趨勢
│   │   ├── recalculate_trends.py   # 重新計算趨勢
│   │   └── test_smart_validation.py # 測試智能驗證
│   └── 📁 maintenance/             # 維護腳本
│       ├── generate_report_now.py  # 立即生成報告
│       └── clean_invalid_entities.py # 清理無效實體
├── 📁 docs/                        # 詳細文檔
│   ├── README.md                   # 主要文檔
│   ├── INSTALLATION.md             # 安裝指南
│   ├── ARCHITECTURE.md             # 系統架構
│   ├── API.md                      # API 文檔
│   ├── USAGE.md                    # 使用指南
│   ├── SCRIPTS.md                  # 腳本索引
│   └── PROJECT_OVERVIEW.md         # 項目總覽
├── 📁 reports/                     # 生成的報告
│   ├── hourly_report_*.html        # 每小時報告
│   ├── daily_report_*.html         # 每日報告
│   ├── latest_hourly.html          # 最新每小時報告
│   └── latest_daily.html           # 最新每日報告
├── 📁 data/                        # 數據文件
│   └── reddit_monitor.db           # SQLite 數據庫
├── 📁 tests/                       # 測試文件
├── 📄 main.py                      # 主程序入口
├── 📄 requirements.txt             # 依賴包列表
└── 📄 README.md                    # 項目說明
```

## 🔄 數據流程圖

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Reddit    │───▶│  數據收集    │───▶│  數據存儲    │
│    API      │    │   模塊      │    │   (SQLite)  │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   報告生成   │◀───│  趨勢分析    │◀───│  實體提取    │
│   模塊      │    │   模塊      │    │   模塊      │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ HTML/JSON   │    │  趨勢分數    │    │  股票/加密   │
│   報告      │    │   計算      │    │  貨幣符號    │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                           ┌─────────────┐    │
                           │  情緒分析    │◀───┘
                           │   模塊      │
                           └─────────────┘
                                  │
                                  ▼
                           ┌─────────────┐
                           │ 情緒分數和   │
                           │  分類結果    │
                           └─────────────┘
```

## 📊 核心功能模塊

### 1. 數據收集模塊 (collectors/)
- **Reddit 收集器**: 從 Reddit API 收集帖子和評論
- **多源收集**: 支持多個 subreddit 同時收集
- **智能調度**: 避免 API 限制，優化收集效率
- **數據清洗**: 過濾無效和重複數據

### 2. 分析模塊 (analyzers/)
- **實體提取器**: 識別股票代碼和加密貨幣符號
- **情緒分析器**: 分析用戶情緒傾向
- **趨勢分析器**: 計算多維度趨勢分數
- **智能驗證器**: 過濾錯誤識別的符號

### 3. 存儲模塊 (storage/)
- **數據庫管理**: SQLite 數據庫操作
- **數據模型**: 定義數據結構和關係
- **索引優化**: 提高查詢性能
- **數據完整性**: 確保數據一致性

### 4. 報告模塊 (reporters/)
- **HTML 報告**: 可視化的網頁報告
- **JSON 報告**: 結構化數據輸出
- **多時間段**: 支持小時、日、週報告
- **實時更新**: 自動生成最新報告

### 5. 調度模塊 (schedulers/)
- **任務調度**: 自動化執行各種任務
- **錯誤恢復**: 自動處理和恢復錯誤
- **性能監控**: 監控系統運行狀態
- **資源管理**: 優化系統資源使用

## 🎯 核心算法

### 趨勢分數計算
```
趨勢分數 = (提及次數 × 0.4) + (正面情緒比例 × 0.3) + (平均情緒分數 × 0.3)
```

### 特殊排名算法
1. **突然爆紅**: `(當前提及 - 之前提及) / 之前提及`
2. **小眾熱門**: `平均情緒分數 / log(提及次數 + 1)`
3. **Meme 潛力**: `表情符號數量 + 感嘆號數量 + 火箭符號數量`
4. **小市值寶石**: `情緒分數 × (1 / 提及次數) × 新穎度因子`

### 智能驗證機制
1. **格式驗證**: 檢查符號格式合理性
2. **黑名單過濾**: 排除常見英文單詞
3. **上下文分析**: 分析周圍文字的金融相關性
4. **API 驗證**: 調用外部 API 確認真實性
5. **信心度評分**: 綜合評估給出最終分數

## 📈 支持的數據源

### Reddit Subreddits
**股票相關**:
- wallstreetbets (15M+ 訂閱者)
- stocks (4.5M+ 訂閱者)
- investing (1.8M+ 訂閱者)
- StockMarket (4.2M+ 訂閱者)
- pennystocks (220K+ 訂閱者)

**加密貨幣相關**:
- CryptoCurrency (6.8M+ 訂閱者)
- Bitcoin (4.9M+ 訂閱者)
- ethereum (1.2M+ 訂閱者)
- dogecoin (2.3M+ 訂閱者)
- CryptoMarkets (1.8M+ 訂閱者)

### 數據類型
- **帖子數據**: 標題、內容、分數、評論數
- **評論數據**: 內容、分數、嵌套關係
- **用戶數據**: 作者信息、發布時間
- **元數據**: 收集時間、來源信息

## 🔧 配置參數

### 收集配置
- `POSTS_PER_SUBREDDIT`: 每個 subreddit 收集帖子數 (默認: 100)
- `COMMENTS_PER_POST`: 每個帖子收集評論數 (默認: 50)
- `COLLECTION_INTERVAL`: 收集間隔秒數 (默認: 3600)

### 分析配置
- `SENTIMENT_CONFIDENCE_THRESHOLD`: 情緒分析信心度閾值 (默認: 0.6)
- `ENTITY_CONFIDENCE_THRESHOLD`: 實體識別信心度閾值 (默認: 0.5)
- `TREND_CALCULATION_INTERVALS`: 趨勢計算時間間隔 (默認: [1, 6, 24, 168])

### 報告配置
- `REPORTS_DIR`: 報告輸出目錄 (默認: "reports")
- `REPORT_RETENTION_DAYS`: 報告保留天數 (默認: 30)
- `AUTO_REPORT_GENERATION`: 自動生成報告 (默認: True)

## 📊 性能指標

### 處理能力
- **帖子處理**: 100 帖子/分鐘
- **評論處理**: 1000 評論/分鐘
- **實體提取**: 500 實體/分鐘
- **情緒分析**: 1000 分析/分鐘

### 響應時間
- **實體提取**: < 100ms
- **情緒分析**: < 50ms
- **趨勢計算**: < 500ms
- **報告生成**: < 2s

### 資源使用
- **內存使用**: 通常 < 1GB
- **CPU 使用**: 中等負載
- **磁盤空間**: 隨數據增長
- **網絡帶寬**: 取決於收集量

## 🛡️ 安全特性

### API 安全
- 環境變量存儲敏感信息
- API 密鑰輪換機制
- 請求速率限制
- 錯誤信息過濾

### 數據安全
- 輸入數據驗證
- SQL 注入防護
- 數據庫訪問控制
- 敏感數據脫敏

### 系統安全
- 日誌記錄和監控
- 異常處理機制
- 資源使用限制
- 定期安全更新

## 🔄 部署選項

### 本地部署
- 適合個人使用和開發
- 完全控制數據和配置
- 無需外部依賴

### 雲端部署
- 適合生產環境
- 自動擴展和備份
- 高可用性保證

### 容器化部署
- 使用 Docker 容器
- 簡化部署和管理
- 環境一致性保證

## 📈 擴展性

### 水平擴展
- 支持多實例並行運行
- 數據庫分片和負載均衡
- 分佈式任務處理

### 功能擴展
- 插件式架構設計
- 新數據源易於接入
- 新分析算法易於添加

### 性能擴展
- 緩存機制優化
- 異步處理支持
- 批量操作優化

## 🎯 使用場景

### 個人投資者
- 發現熱門投資機會
- 監控市場情緒變化
- 識別潛在風險

### 量化交易
- 情緒因子構建
- 趨勢信號生成
- 風險管理輔助

### 市場研究
- 社交媒體情緒分析
- 投資者行為研究
- 市場趨勢預測

### 金融機構
- 客戶情緒監控
- 產品推薦優化
- 風險評估輔助

這個項目提供了一個完整的、可擴展的金融市場情緒分析解決方案，適合各種規模的用戶和應用場景。
