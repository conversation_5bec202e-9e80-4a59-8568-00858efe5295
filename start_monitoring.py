#!/usr/bin/env python3
"""
Reddit 監控系統啟動腳本
自動處理虛擬環境和系統啟動
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_venv():
    """檢查虛擬環境是否存在"""
    venv_path = Path("venv")
    if not venv_path.exists():
        print("❌ 虛擬環境不存在，請先運行 setup.py")
        return False
    return True

def check_env_file():
    """檢查 .env 文件是否存在並配置"""
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env 文件不存在")
        print("📝 請複製 .env.sample 到 .env 並填入正確的 API 憑證")
        return False
    
    # 檢查基本配置
    with open(env_path, 'r') as f:
        content = f.read()
        if 'your_reddit_client_id' in content:
            print("⚠️  .env 文件中仍有預設值，請填入正確的 API 憑證")
            return False
    
    return True

def start_monitoring():
    """啟動監控系統"""
    print("🚀 啟動 Reddit 股票加密貨幣監控系統...")
    
    # 檢查前置條件
    if not check_venv():
        return
    
    if not check_env_file():
        return
    
    # 啟動系統
    try:
        if sys.platform == "win32":
            activate_script = "venv\\Scripts\\activate"
            cmd = f"{activate_script} && python main.py"
        else:
            activate_script = "source venv/bin/activate"
            cmd = f"{activate_script} && python main.py"
        
        print("📊 系統正在啟動...")
        print("🔄 監控將每小時收集數據並生成報告")
        print("📈 報告將保存在 reports/ 目錄中")
        print("⏹️  按 Ctrl+C 停止監控")
        print("=" * 60)
        
        subprocess.run(cmd, shell=True, cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n⏹️  監控已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")

def show_help():
    """顯示幫助信息"""
    print("""
Reddit 股票加密貨幣監控系統 - 啟動腳本

使用方法:
    python start_monitoring.py              # 啟動完整監控系統
    python start_monitoring.py --test       # 運行測試模式
    python start_monitoring.py --validate   # 驗證系統配置
    python start_monitoring.py --help       # 顯示此幫助

前置條件:
1. 已安裝 Python 3.9+
2. 已創建虛擬環境 (venv/)
3. 已配置 .env 文件中的 API 憑證

API 憑證獲取:
- Reddit API: https://www.reddit.com/prefs/apps
- OpenRouter API: https://openrouter.ai/keys

目錄結構:
- data/: SQLite 數據庫
- reports/: 生成的 HTML 和 JSON 報告
- logs/: 系統日誌文件
""")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            show_help()
        elif sys.argv[1] == "--test":
            os.system("source venv/bin/activate && python main.py --test")
        elif sys.argv[1] == "--validate":
            os.system("source venv/bin/activate && python main.py --validate")
        else:
            print("❌ 未知參數，使用 --help 查看幫助")
    else:
        start_monitoring()