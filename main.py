#!/usr/bin/env python3
"""
Reddit 股票加密貨幣情緒監控系統
主程序入口

使用方法:
    python main.py                    # 啟動完整的監控系統
    python main.py --test             # 運行測試模式
    python main.py --collect          # 只運行數據收集
    python main.py --analyze          # 只運行分析
    python main.py --report           # 只生成報告
    python main.py --daemon           # 後台運行模式
"""

import sys
import os
import argparse
import signal
import logging
from datetime import datetime

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import Config
from src.schedulers.task_scheduler import TaskScheduler
from src.collectors.reddit_collector import RedditCollector
from src.analyzers.ollama_sentiment_analyzer import OllamaSentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor
from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator
from src.storage.database import DatabaseManager


class RedditMonitorApp:
    """Reddit 監控應用主類"""
    
    def __init__(self):
        self.config = Config()
        self.logger = self._setup_logger()
        self.scheduler = None
        self.running = False
        
        # 設置信號處理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logger(self) -> logging.Logger:
        """設置主日誌記錄器"""
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger('RedditMonitor')
        return logger
    
    def _signal_handler(self, signum, frame):
        """信號處理器"""
        self.logger.info(f"收到信號 {signum}，正在停止系統...")
        self.stop()
    
    def validate_system(self) -> bool:
        """驗證系統配置和連接"""
        self.logger.info("🔧 驗證系統配置...")
        
        # 驗證配置
        if not self.config.validate():
            return False
        
        try:
            # 測試數據庫連接
            db = DatabaseManager()
            db.setup_default_subreddits()
            self.logger.info("✅ 數據庫連接正常")
            
            # 測試 Reddit API
            reddit_collector = RedditCollector()
            if not reddit_collector.test_connection():
                return False
            
            # 測試 Ollama API
            sentiment_analyzer = OllamaSentimentAnalyzer()
            if not sentiment_analyzer.test_api_connection():
                return False
            
            self.logger.info("✅ 系統驗證通過")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 系統驗證失敗: {e}")
            return False
    
    def run_test_mode(self):
        """運行測試模式"""
        self.logger.info("🧪 運行測試模式...")
        
        try:
            # 收集少量測試數據
            reddit_collector = RedditCollector()
            posts = reddit_collector.collect_subreddit_posts("stocks", limit=3)
            self.logger.info(f"收集了 {len(posts)} 個測試帖子")
            
            # 分析情緒
            sentiment_analyzer = OllamaSentimentAnalyzer()
            entity_extractor = EntityExtractor()
            
            for post in posts:
                # 情緒分析
                sentiment = sentiment_analyzer.analyze_sentiment(
                    f"{post['title']} {post['content']}", 
                    post['id'], 
                    'post'
                )
                
                # 實體提取
                entities = entity_extractor.extract_entities(
                    f"{post['title']} {post['content']}", 
                    post['id'], 
                    'post'
                )
                
                self.logger.info(f"帖子 {post['id']}: 情緒={sentiment['sentiment_label'] if sentiment else 'N/A'}, 實體={len(entities)}")
            
            # 計算趨勢
            trend_analyzer = TrendAnalyzer()
            trends = trend_analyzer.calculate_all_trends()
            self.logger.info(f"趨勢計算: {trends}")
            
            # 生成測試報告
            report_generator = ReportGenerator()
            report_path = report_generator.generate_hourly_report()
            self.logger.info(f"測試報告生成: {report_path}")
            
            self.logger.info("✅ 測試模式完成")
            
        except Exception as e:
            self.logger.error(f"❌ 測試模式失敗: {e}")
            raise
    
    def run_collect_only(self):
        """只運行數據收集"""
        self.logger.info("📥 運行數據收集模式...")
        
        try:
            reddit_collector = RedditCollector()
            
            # 收集股票數據
            stock_results = reddit_collector.collect_all_subreddits("stock")
            self.logger.info(f"股票數據: {stock_results}")
            
            # 收集加密貨幣數據
            crypto_results = reddit_collector.collect_all_subreddits("crypto")
            self.logger.info(f"加密貨幣數據: {crypto_results}")
            
            self.logger.info("✅ 數據收集完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據收集失敗: {e}")
            raise
    
    def run_analyze_only(self):
        """只運行分析"""
        self.logger.info("🔍 運行分析模式...")
        
        try:
            # 情緒分析
            sentiment_analyzer = OllamaSentimentAnalyzer()
            posts_analyzed = sentiment_analyzer.analyze_recent_posts(hours=24)
            comments_analyzed = sentiment_analyzer.analyze_recent_comments(hours=24)
            
            # 實體提取
            entity_extractor = EntityExtractor()
            posts_entities = entity_extractor.extract_from_recent_posts(hours=24)
            comments_entities = entity_extractor.extract_from_recent_comments(hours=24)
            
            # 趨勢計算
            trend_analyzer = TrendAnalyzer()
            trends = trend_analyzer.calculate_all_trends()
            
            self.logger.info(f"分析完成: 情緒={posts_analyzed + comments_analyzed}, 實體={posts_entities + comments_entities}, 趨勢={trends}")
            
        except Exception as e:
            self.logger.error(f"❌ 分析失敗: {e}")
            raise
    
    def run_report_only(self):
        """只生成報告"""
        self.logger.info("📊 運行報告生成模式...")
        
        try:
            report_generator = ReportGenerator()
            
            # 生成各種報告
            hourly_report = report_generator.generate_hourly_report()
            daily_report = report_generator.generate_daily_report()
            weekly_report = report_generator.generate_weekly_report()
            
            self.logger.info(f"報告生成完成:")
            self.logger.info(f"  - 每小時: {hourly_report}")
            self.logger.info(f"  - 每日: {daily_report}")
            self.logger.info(f"  - 每週: {weekly_report}")
            
        except Exception as e:
            self.logger.error(f"❌ 報告生成失敗: {e}")
            raise
    
    def start_scheduler(self):
        """啟動調度器"""
        self.logger.info("🚀 啟動 Reddit 股票加密貨幣情緒監控系統...")
        
        try:
            self.scheduler = TaskScheduler()
            self.running = True
            
            # 顯示系統信息
            self.logger.info("=" * 60)
            self.logger.info("Reddit 股票加密貨幣情緒監控系統")
            self.logger.info("=" * 60)
            self.logger.info(f"啟動時間: {datetime.now()}")
            self.logger.info(f"配置文件: .env")
            self.logger.info(f"數據庫: {self.config.DATABASE_PATH}")
            self.logger.info(f"報告目錄: {self.config.REPORTS_DIR}")
            self.logger.info("=" * 60)
            
            # 啟動調度器
            self.scheduler.start()
            
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號")
        except Exception as e:
            self.logger.error(f"❌ 調度器啟動失敗: {e}")
            raise
        finally:
            self.stop()
    
    def stop(self):
        """停止系統"""
        if self.running:
            self.logger.info("⏹️ 停止系統...")
            self.running = False
            
            if self.scheduler:
                self.scheduler.stop()
            
            self.logger.info("✅ 系統已停止")


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='Reddit 股票加密貨幣情緒監控系統')
    parser.add_argument('--test', action='store_true', help='運行測試模式')
    parser.add_argument('--collect', action='store_true', help='只運行數據收集')
    parser.add_argument('--analyze', action='store_true', help='只運行分析')
    parser.add_argument('--report', action='store_true', help='只生成報告')
    parser.add_argument('--daemon', action='store_true', help='後台運行模式')
    parser.add_argument('--validate', action='store_true', help='只驗證系統配置')
    
    args = parser.parse_args()
    
    # 創建應用實例
    app = RedditMonitorApp()
    
    try:
        # 驗證系統
        if not app.validate_system():
            print("❌ 系統驗證失敗，請檢查配置")
            sys.exit(1)
        
        if args.validate:
            print("✅ 系統驗證通過")
            return
        
        # 根據參數運行不同模式
        if args.test:
            app.run_test_mode()
        elif args.collect:
            app.run_collect_only()
        elif args.analyze:
            app.run_analyze_only()
        elif args.report:
            app.run_report_only()
        else:
            # 默認啟動完整的監控系統
            app.start_scheduler()
    
    except Exception as e:
        app.logger.error(f"❌ 應用運行失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
