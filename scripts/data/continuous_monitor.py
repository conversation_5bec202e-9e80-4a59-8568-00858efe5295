#!/usr/bin/env python3
"""
持續監控系統
24/7 運行，定期收集和分析數據
"""

import sys
import os
import time
import schedule
from datetime import datetime

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import Config
from src.collectors.reddit_collector import RedditCollector
from src.analyzers.sentiment_analyzer import SentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor
from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator


class ContinuousMonitor:
    """持續監控系統"""
    
    def __init__(self):
        self.config = Config()
        self.reddit_collector = RedditCollector()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.entity_extractor = EntityExtractor()
        self.trend_analyzer = TrendAnalyzer()
        
        print("🚀 持續監控系統初始化完成")
        print(f"⏰ 當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def collect_routine(self):
        """定期收集例程"""
        try:
            print(f"\n📥 [{datetime.now().strftime('%H:%M:%S')}] 開始定期收集...")
            
            # 收集新數據
            results = self.reddit_collector.collect_all_subreddits()
            
            print(f"✅ 收集完成: {results}")
            
        except Exception as e:
            print(f"❌ 收集失敗: {e}")
    
    def analysis_routine(self):
        """定期分析例程"""
        try:
            print(f"\n🔍 [{datetime.now().strftime('%H:%M:%S')}] 開始定期分析...")
            
            # 情緒分析
            sentiment_count = self.sentiment_analyzer.analyze_recent_posts(hours=2)
            comment_sentiment_count = self.sentiment_analyzer.analyze_recent_comments(hours=2)
            
            # 實體提取
            entity_count = self.entity_extractor.extract_from_recent_posts(hours=2)
            comment_entity_count = self.entity_extractor.extract_from_recent_comments(hours=2)
            
            # 趨勢計算
            trend_results = self.trend_analyzer.calculate_all_trends()
            
            print(f"✅ 分析完成:")
            print(f"  情緒分析: {sentiment_count + comment_sentiment_count}")
            print(f"  實體提取: {entity_count + comment_entity_count}")
            print(f"  趨勢計算: {trend_results}")
            
        except Exception as e:
            print(f"❌ 分析失敗: {e}")
    
    def report_routine(self):
        """定期報告例程"""
        try:
            print(f"\n📊 [{datetime.now().strftime('%H:%M:%S')}] 生成報告...")
            
            report_generator = ReportGenerator()
            report_path = report_generator.generate_hourly_report()
            
            print(f"✅ 報告生成: {report_path}")
            
        except Exception as e:
            print(f"❌ 報告生成失敗: {e}")
    
    def quick_collect(self):
        """快速收集（少量數據）"""
        try:
            print(f"\n⚡ [{datetime.now().strftime('%H:%M:%S')}] 快速收集...")
            
            # 只收集熱門 subreddits 的少量新帖子
            priority_subreddits = ['wallstreetbets', 'stocks', 'CryptoCurrency', 'Bitcoin']
            
            total_collected = 0
            for subreddit_name in priority_subreddits:
                try:
                    posts = self.reddit_collector.collect_subreddit_posts(
                        subreddit_name, 
                        limit=10,  # 每個只收集10個帖子
                        post_types=['hot', 'new']
                    )
                    total_collected += len(posts)
                    
                    # 收集部分評論
                    for post in posts[:3]:  # 只為前3個帖子收集評論
                        comments = self.reddit_collector.collect_post_comments(post['id'], limit=10)
                    
                except Exception as e:
                    print(f"  ⚠️ 收集 r/{subreddit_name} 失敗: {e}")
                    continue
                
                time.sleep(2)  # 避免 API 限制
            
            print(f"✅ 快速收集完成: {total_collected} 個帖子")
            
        except Exception as e:
            print(f"❌ 快速收集失敗: {e}")
    
    def setup_schedule(self):
        """設置調度"""
        print("⏰ 設置調度任務...")
        
        # 每15分鐘快速收集
        schedule.every(15).minutes.do(self.quick_collect)
        
        # 每30分鐘分析
        schedule.every(30).minutes.do(self.analysis_routine)
        
        # 每小時生成報告
        schedule.every().hour.do(self.report_routine)
        
        # 每6小時大量收集
        schedule.every(6).hours.do(self.collect_routine)
        
        print("✅ 調度設置完成:")
        print("  📥 每15分鐘: 快速收集")
        print("  🔍 每30分鐘: 分析數據")
        print("  📊 每1小時: 生成報告")
        print("  📦 每6小時: 大量收集")
    
    def run(self):
        """運行持續監控"""
        print("\n🚀 啟動持續監控系統...")
        print("按 Ctrl+C 停止")
        print("=" * 60)
        
        # 設置調度
        self.setup_schedule()
        
        # 立即執行一次快速收集和分析
        print("🎯 執行初始收集和分析...")
        self.quick_collect()
        time.sleep(5)
        self.analysis_routine()
        time.sleep(5)
        self.report_routine()
        
        # 開始調度循環
        print(f"\n🔄 開始調度循環...")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
                
                # 每10分鐘顯示狀態
                if datetime.now().minute % 10 == 0:
                    print(f"💓 [{datetime.now().strftime('%H:%M:%S')}] 系統運行中...")
                    
        except KeyboardInterrupt:
            print("\n⏹️ 收到停止信號，正在關閉...")
            print("👋 持續監控系統已停止")


def main():
    """主函數"""
    print("🧪 Reddit 股票加密貨幣監控系統 - 持續監控")
    print("=" * 80)
    
    # 驗證配置
    config = Config()
    if not config.validate():
        print("❌ 配置驗證失敗，請檢查 .env 文件")
        return False
    
    try:
        monitor = ContinuousMonitor()
        monitor.run()
        return True
        
    except KeyboardInterrupt:
        print("\n👋 監控停止")
        return True
    except Exception as e:
        print(f"\n❌ 監控失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
