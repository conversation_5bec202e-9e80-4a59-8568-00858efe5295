#!/usr/bin/env python3
"""
檢查數據庫統計信息
"""

import sys
import os
from datetime import datetime, timedelta

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.storage.database import DatabaseManager


def check_database_stats():
    """檢查數據庫統計"""
    print("📊 檢查數據庫統計信息...")
    print("=" * 60)
    
    db = DatabaseManager()
    conn = db.models.get_connection()
    cursor = conn.cursor()
    
    # 總體統計
    print("📈 總體統計:")
    
    cursor.execute('SELECT COUNT(*) FROM posts')
    total_posts = cursor.fetchone()[0]
    print(f"  總帖子數: {total_posts}")
    
    cursor.execute('SELECT COUNT(*) FROM comments')
    total_comments = cursor.fetchone()[0]
    print(f"  總評論數: {total_comments}")
    
    cursor.execute('SELECT COUNT(*) FROM sentiment_analysis')
    total_sentiment = cursor.fetchone()[0]
    print(f"  總情緒分析: {total_sentiment}")
    
    cursor.execute('SELECT COUNT(*) FROM entities')
    total_entities = cursor.fetchone()[0]
    print(f"  總實體提取: {total_entities}")
    
    cursor.execute('SELECT COUNT(*) FROM trends')
    total_trends = cursor.fetchone()[0]
    print(f"  總趨勢記錄: {total_trends}")
    
    # 時間分布統計
    print("\n⏰ 時間分布統計:")
    
    time_periods = [
        ("最近1小時", "1 hour"),
        ("最近6小時", "6 hours"),
        ("最近24小時", "24 hours"),
        ("最近7天", "7 days")
    ]
    
    for period_name, period_sql in time_periods:
        print(f"\n{period_name}:")
        
        cursor.execute(f'SELECT COUNT(*) FROM posts WHERE created_utc >= datetime("now", "-{period_sql}")')
        posts_count = cursor.fetchone()[0]
        
        cursor.execute(f'SELECT COUNT(*) FROM comments WHERE created_utc >= datetime("now", "-{period_sql}")')
        comments_count = cursor.fetchone()[0]
        
        cursor.execute(f'SELECT COUNT(*) FROM sentiment_analysis WHERE analyzed_at >= datetime("now", "-{period_sql}")')
        sentiment_count = cursor.fetchone()[0]
        
        cursor.execute(f'SELECT COUNT(*) FROM entities WHERE extracted_at >= datetime("now", "-{period_sql}")')
        entities_count = cursor.fetchone()[0]
        
        print(f"  帖子: {posts_count}, 評論: {comments_count}, 情緒: {sentiment_count}, 實體: {entities_count}")
    
    # 最新數據時間
    print("\n🕐 最新數據時間:")
    
    cursor.execute('SELECT MAX(created_utc) FROM posts')
    latest_post = cursor.fetchone()[0]
    print(f"  最新帖子: {latest_post}")
    
    cursor.execute('SELECT MAX(created_utc) FROM comments')
    latest_comment = cursor.fetchone()[0]
    print(f"  最新評論: {latest_comment}")
    
    cursor.execute('SELECT MAX(analyzed_at) FROM sentiment_analysis')
    latest_sentiment = cursor.fetchone()[0]
    print(f"  最新情緒分析: {latest_sentiment}")
    
    cursor.execute('SELECT MAX(extracted_at) FROM entities')
    latest_entity = cursor.fetchone()[0]
    print(f"  最新實體提取: {latest_entity}")
    
    # 熱門實體統計
    print("\n🔥 熱門實體 (所有時間):")
    
    cursor.execute('''
        SELECT symbol, entity_type, COUNT(*) as count
        FROM entities 
        GROUP BY symbol, entity_type
        ORDER BY count DESC
        LIMIT 20
    ''')
    
    entities = cursor.fetchall()
    for symbol, entity_type, count in entities:
        print(f"  {symbol} ({entity_type}): {count} 次提及")
    
    # Subreddit 統計
    print("\n📱 Subreddit 統計:")
    
    cursor.execute('''
        SELECT s.name, COUNT(p.id) as post_count
        FROM subreddits s
        LEFT JOIN posts p ON s.id = p.subreddit_id
        GROUP BY s.id, s.name
        ORDER BY post_count DESC
        LIMIT 15
    ''')
    
    subreddits = cursor.fetchall()
    for name, post_count in subreddits:
        print(f"  r/{name}: {post_count} 個帖子")
    
    conn.close()


if __name__ == "__main__":
    check_database_stats()
