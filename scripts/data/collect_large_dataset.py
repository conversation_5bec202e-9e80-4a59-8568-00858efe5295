#!/usr/bin/env python3
"""
大量數據收集腳本
收集1000+個帖子用於有意義的趨勢分析
"""

import sys
import os
import time
from datetime import datetime
from typing import Dict, Any

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import Config
from src.collectors.reddit_collector import RedditCollector
from src.analyzers.sentiment_analyzer import SentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor
from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator
from src.storage.database import DatabaseManager


class LargeDatasetCollector:
    """大量數據集收集器"""
    
    def __init__(self):
        self.config = Config()
        self.reddit_collector = RedditCollector()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.entity_extractor = EntityExtractor()
        self.trend_analyzer = TrendAnalyzer()
        self.db = DatabaseManager()
        
        print("🚀 大量數據收集系統初始化完成")
    
    def collect_massive_dataset(self, target_posts: int = 1000):
        """收集大量數據集"""
        print(f"📊 開始收集大量數據集，目標: {target_posts} 個帖子")
        print("=" * 80)
        
        start_time = time.time()
        
        # 階段1：批量收集帖子
        print("📥 階段1: 批量收集帖子...")
        collection_results = self.reddit_collector.bulk_collect_data(
            target_posts=target_posts,
            max_subreddits=15  # 增加 subreddit 數量
        )
        
        print(f"✅ 收集完成: {collection_results}")
        
        # 階段2：情緒分析
        print("\n🎭 階段2: 批量情緒分析...")
        sentiment_results = self._batch_sentiment_analysis()
        
        # 階段3：實體提取
        print("\n🔍 階段3: 批量實體提取...")
        entity_results = self._batch_entity_extraction()
        
        # 階段4：趨勢計算
        print("\n📈 階段4: 趨勢計算...")
        trend_results = self.trend_analyzer.calculate_all_trends()
        
        # 階段5：生成報告
        print("\n📊 階段5: 生成詳細報告...")
        report_generator = ReportGenerator()
        report_path = report_generator.generate_hourly_report()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 統計結果
        final_stats = self._get_collection_stats()
        
        print("\n" + "=" * 80)
        print("🎉 大量數據收集完成！")
        print("=" * 80)
        print(f"⏱️  總耗時: {total_time/60:.1f} 分鐘")
        print(f"📊 統計結果:")
        print(f"  📝 總帖子數: {final_stats['total_posts']}")
        print(f"  💬 總評論數: {final_stats['total_comments']}")
        print(f"  🎭 情緒分析: {final_stats['sentiment_analyzed']}")
        print(f"  🔍 實體提取: {final_stats['entities_extracted']}")
        print(f"  📈 股票趨勢: {final_stats['stock_trends']}")
        print(f"  💰 加密貨幣趨勢: {final_stats['crypto_trends']}")
        print(f"  📊 報告路徑: {report_path}")
        
        return {
            'collection_results': collection_results,
            'sentiment_results': sentiment_results,
            'entity_results': entity_results,
            'trend_results': trend_results,
            'report_path': report_path,
            'final_stats': final_stats,
            'total_time': total_time
        }
    
    def _batch_sentiment_analysis(self) -> Dict[str, int]:
        """批量情緒分析"""
        # 分析最近收集的帖子
        posts_analyzed = self.sentiment_analyzer.analyze_recent_posts(hours=2)
        
        # 分析最近收集的評論
        comments_analyzed = self.sentiment_analyzer.analyze_recent_comments(hours=2)
        
        results = {
            'posts_analyzed': posts_analyzed,
            'comments_analyzed': comments_analyzed,
            'total_analyzed': posts_analyzed + comments_analyzed
        }
        
        print(f"  ✅ 情緒分析完成: {results['total_analyzed']} 個項目")
        return results
    
    def _batch_entity_extraction(self) -> Dict[str, int]:
        """批量實體提取"""
        # 從最近的帖子提取實體
        posts_entities = self.entity_extractor.extract_from_recent_posts(hours=2)
        
        # 從最近的評論提取實體
        comments_entities = self.entity_extractor.extract_from_recent_comments(hours=2)
        
        results = {
            'posts_entities': posts_entities,
            'comments_entities': comments_entities,
            'total_entities': posts_entities + comments_entities
        }
        
        print(f"  ✅ 實體提取完成: {results['total_entities']} 個實體")
        return results
    
    def _get_collection_stats(self) -> Dict[str, int]:
        """獲取收集統計"""
        conn = self.db.models.get_connection()
        cursor = conn.cursor()
        
        # 總帖子數
        cursor.execute('SELECT COUNT(*) FROM posts WHERE created_utc >= datetime("now", "-2 hours")')
        total_posts = cursor.fetchone()[0]
        
        # 總評論數
        cursor.execute('SELECT COUNT(*) FROM comments WHERE created_utc >= datetime("now", "-2 hours")')
        total_comments = cursor.fetchone()[0]
        
        # 情緒分析數
        cursor.execute('SELECT COUNT(*) FROM sentiment_analysis WHERE analyzed_at >= datetime("now", "-2 hours")')
        sentiment_analyzed = cursor.fetchone()[0]
        
        # 實體提取數
        cursor.execute('SELECT COUNT(*) FROM entities WHERE extracted_at >= datetime("now", "-2 hours")')
        entities_extracted = cursor.fetchone()[0]
        
        # 股票趨勢數
        cursor.execute('SELECT COUNT(DISTINCT symbol) FROM trends WHERE entity_type = "stock" AND calculated_at >= datetime("now", "-1 hour")')
        stock_trends = cursor.fetchone()[0]
        
        # 加密貨幣趨勢數
        cursor.execute('SELECT COUNT(DISTINCT symbol) FROM trends WHERE entity_type = "crypto" AND calculated_at >= datetime("now", "-1 hour")')
        crypto_trends = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_posts': total_posts,
            'total_comments': total_comments,
            'sentiment_analyzed': sentiment_analyzed,
            'entities_extracted': entities_extracted,
            'stock_trends': stock_trends,
            'crypto_trends': crypto_trends
        }
    
    def collect_targeted_subreddits(self, target_posts_per_subreddit: int = 100):
        """針對性收集特定 subreddits"""
        print(f"🎯 針對性收集，每個 subreddit {target_posts_per_subreddit} 個帖子")
        
        # 高價值 subreddits（按訂閱者和活躍度排序）
        priority_subreddits = [
            'wallstreetbets',    # 15M+ 訂閱者
            'stocks',            # 4.5M+ 訂閱者
            'CryptoCurrency',    # 6.8M+ 訂閱者
            'investing',         # 1.8M+ 訂閱者
            'StockMarket',       # 4.2M+ 訂閱者
            'Bitcoin',           # 4.9M+ 訂閱者
            'ethereum',          # 1.2M+ 訂閱者
            'dogecoin',          # 2.3M+ 訂閱者
            'CryptoMarkets',     # 1.8M+ 訂閱者
            'pennystocks'        # 220K+ 訂閱者
        ]
        
        total_collected = 0
        
        for i, subreddit_name in enumerate(priority_subreddits, 1):
            try:
                print(f"📥 [{i}/{len(priority_subreddits)}] 收集 r/{subreddit_name}...")
                
                # 使用多樣化收集方法
                posts = self.reddit_collector.collect_diverse_posts(
                    subreddit_name, 
                    total_limit=target_posts_per_subreddit
                )
                
                total_collected += len(posts)
                print(f"  ✅ 收集了 {len(posts)} 個帖子，總計: {total_collected}")
                
                # 為部分帖子收集評論
                comments_collected = 0
                for post in posts[:20]:  # 前20個帖子
                    try:
                        comments = self.reddit_collector.collect_post_comments(post['id'], limit=30)
                        comments_collected += len(comments)
                    except Exception as e:
                        print(f"    ⚠️ 收集評論失敗: {e}")
                        continue
                
                print(f"  💬 收集了 {comments_collected} 個評論")
                
                # 避免 API 限制
                time.sleep(5)
                
            except Exception as e:
                print(f"  ❌ 收集 r/{subreddit_name} 失敗: {e}")
                continue
        
        print(f"🎉 針對性收集完成，總計: {total_collected} 個帖子")
        return total_collected


def main():
    """主函數"""
    print("🧪 Reddit 股票加密貨幣監控系統 - 大量數據收集")
    print("=" * 80)
    
    # 驗證配置
    config = Config()
    if not config.validate():
        print("❌ 配置驗證失敗，請檢查 .env 文件")
        return False
    
    try:
        collector = LargeDatasetCollector()
        
        # 選擇收集模式
        print("選擇收集模式:")
        print("1. 快速收集 (500 個帖子)")
        print("2. 標準收集 (1000 個帖子)")
        print("3. 大量收集 (2000 個帖子)")
        print("4. 針對性收集 (高質量 subreddits)")
        
        try:
            choice = input("請選擇 (1-4): ").strip()
        except KeyboardInterrupt:
            print("\n👋 收集取消")
            return False
        
        if choice == "1":
            results = collector.collect_massive_dataset(target_posts=500)
        elif choice == "2":
            results = collector.collect_massive_dataset(target_posts=1000)
        elif choice == "3":
            results = collector.collect_massive_dataset(target_posts=2000)
        elif choice == "4":
            total_posts = collector.collect_targeted_subreddits(target_posts_per_subreddit=150)
            # 然後進行分析
            results = collector.collect_massive_dataset(target_posts=0)  # 只分析，不再收集
        else:
            print("❌ 無效選擇，使用默認模式")
            results = collector.collect_massive_dataset(target_posts=1000)
        
        print("\n🎯 收集任務完成！")
        print(f"📊 查看詳細報告: {results['report_path']}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 收到中斷信號，正在停止...")
        return False
    except Exception as e:
        print(f"\n❌ 收集失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
