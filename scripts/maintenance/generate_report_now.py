#!/usr/bin/env python3
"""
立即生成報告
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.reporters.report_generator import ReportGenerator

def main():
    print("📊 立即生成報告...")
    
    try:
        report_generator = ReportGenerator()
        report_path = report_generator.generate_hourly_report()
        
        print(f"✅ 報告生成成功: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"❌ 報告生成失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
