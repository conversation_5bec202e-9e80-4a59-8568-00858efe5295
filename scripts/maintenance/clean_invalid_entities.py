#!/usr/bin/env python3
"""
清理無效的實體
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.storage.database import DatabaseManager
from src.analyzers.smart_symbol_validator import SmartSymbolValidator

def clean_invalid_entities():
    """清理無效的實體"""
    print("🧹 清理無效的實體...")
    
    db = DatabaseManager()
    validator = SmartSymbolValidator()
    
    conn = db.models.get_connection()
    cursor = conn.cursor()
    
    # 獲取所有實體
    cursor.execute('SELECT DISTINCT symbol, entity_type FROM entities')
    entities = cursor.fetchall()
    
    invalid_entities = []
    valid_entities = []
    
    print(f"📊 檢查 {len(entities)} 個唯一實體...")
    
    for symbol, entity_type in entities:
        # 使用智能驗證器驗證
        is_valid, confidence, reason = validator.validate_symbol(symbol, "", entity_type)
        
        if not is_valid or confidence < 0.5:
            invalid_entities.append((symbol, entity_type, reason))
            print(f"❌ 無效: {symbol} ({entity_type}) - {reason}")
        else:
            valid_entities.append((symbol, entity_type))
            print(f"✅ 有效: {symbol} ({entity_type}) - 信心度: {confidence:.2f}")
    
    print(f"\n📈 統計結果:")
    print(f"  有效實體: {len(valid_entities)}")
    print(f"  無效實體: {len(invalid_entities)}")
    
    if invalid_entities:
        print(f"\n🗑️ 清理無效實體...")
        
        for symbol, entity_type, reason in invalid_entities:
            # 刪除實體記錄
            cursor.execute('DELETE FROM entities WHERE symbol = ? AND entity_type = ?', (symbol, entity_type))
            
            # 刪除相關的趨勢記錄
            cursor.execute('DELETE FROM trends WHERE symbol = ? AND entity_type = ?', (symbol, entity_type))
            
            print(f"  🗑️ 已刪除: {symbol} ({entity_type})")
        
        conn.commit()
        print(f"✅ 清理完成，刪除了 {len(invalid_entities)} 個無效實體")
        
        # 重新計算趨勢
        print(f"\n🔄 重新計算趨勢...")
        from src.analyzers.trend_analyzer import TrendAnalyzer
        analyzer = TrendAnalyzer()
        results = analyzer.calculate_all_trends()
        print(f"✅ 趨勢重新計算完成: {results}")
        
    else:
        print("✅ 沒有發現無效實體")
    
    conn.close()

if __name__ == "__main__":
    clean_invalid_entities()
