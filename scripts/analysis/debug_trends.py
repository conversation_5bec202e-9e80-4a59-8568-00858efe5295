#!/usr/bin/env python3
"""
調試趨勢數據
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.storage.database import DatabaseManager
from src.analyzers.trend_analyzer import TrendAnalyzer

def debug_trends():
    """調試趨勢數據"""
    print("🔍 調試趨勢數據...")
    print("=" * 60)
    
    db = DatabaseManager()
    conn = db.models.get_connection()
    cursor = conn.cursor()
    
    # 檢查趨勢表結構
    print("📊 趨勢表結構:")
    cursor.execute("PRAGMA table_info(trends)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 檢查趨勢數據
    print("\n📈 趨勢數據統計:")
    cursor.execute('SELECT COUNT(*) FROM trends')
    total_trends = cursor.fetchone()[0]
    print(f"  總趨勢記錄: {total_trends}")
    
    # 按時間段統計
    print("\n⏰ 按時間段統計:")
    cursor.execute('SELECT time_period, COUNT(*) FROM trends GROUP BY time_period')
    time_periods = cursor.fetchall()
    for period, count in time_periods:
        print(f"  {period}: {count} 條記錄")
    
    # 按實體類型統計
    print("\n🏷️ 按實體類型統計:")
    cursor.execute('SELECT entity_type, COUNT(*) FROM trends GROUP BY entity_type')
    entity_types = cursor.fetchall()
    for entity_type, count in entity_types:
        print(f"  {entity_type}: {count} 條記錄")
    
    # 檢查最新趨勢數據
    print("\n🕐 最新趨勢數據:")
    cursor.execute('SELECT MAX(calculated_at) FROM trends')
    latest_trend = cursor.fetchone()[0]
    print(f"  最新計算時間: {latest_trend}")
    
    # 檢查具體的趨勢數據
    print("\n🔥 最新趨勢記錄 (前10條):")
    cursor.execute('''
        SELECT entity_type, symbol, time_period, mention_count, trend_score, calculated_at
        FROM trends 
        ORDER BY calculated_at DESC 
        LIMIT 10
    ''')
    
    trends = cursor.fetchall()
    for entity_type, symbol, time_period, mention_count, trend_score, calculated_at in trends:
        print(f"  {symbol} ({entity_type}) - {time_period}: 提及{mention_count}次, 分數{trend_score:.2f}, 時間{calculated_at}")
    
    # 測試趨勢分析器
    print("\n🧪 測試趨勢分析器:")
    analyzer = TrendAnalyzer()
    
    # 測試不同時間段
    test_periods = ["1h", "6h", "24h", "7d"]
    for period in test_periods:
        try:
            stocks = analyzer.get_top_trending("stock", period, 5)
            cryptos = analyzer.get_top_trending("crypto", period, 5)
            print(f"  {period}: {len(stocks)} 股票, {len(cryptos)} 加密貨幣")
            
            if stocks:
                print(f"    股票: {[s['symbol'] for s in stocks[:3]]}")
            if cryptos:
                print(f"    加密貨幣: {[c['symbol'] for c in cryptos[:3]]}")
                
        except Exception as e:
            print(f"  {period}: 錯誤 - {e}")
    
    # 手動計算趨勢
    print("\n🔄 手動計算趨勢...")
    try:
        results = analyzer.calculate_all_trends()
        print(f"  計算結果: {results}")
    except Exception as e:
        print(f"  計算失敗: {e}")
        import traceback
        traceback.print_exc()
    
    conn.close()

if __name__ == "__main__":
    debug_trends()
