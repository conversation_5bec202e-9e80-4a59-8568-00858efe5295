#!/usr/bin/env python3
"""
重新計算趨勢數據
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator

def main():
    print("🔄 重新計算趨勢數據...")
    
    try:
        # 計算趨勢
        analyzer = TrendAnalyzer()
        results = analyzer.calculate_all_trends()
        
        print(f"✅ 趨勢計算完成: {results}")
        
        # 生成新報告
        print("\n📊 生成新報告...")
        report_generator = ReportGenerator()
        report_path = report_generator.generate_hourly_report()
        
        print(f"✅ 報告生成完成: {report_path}")
        
        return report_path
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
