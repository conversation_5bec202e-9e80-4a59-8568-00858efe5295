#!/usr/bin/env python3
"""
測試智能符號驗證系統
驗證能否正確識別真實的股票/加密貨幣符號，並過濾掉錯誤識別
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analyzers.smart_symbol_validator import SmartSymbolValidator
from src.analyzers.entity_extractor import EntityExtractor


def test_smart_validator():
    """測試智能驗證器"""
    print("🧠 測試智能符號驗證器...")
    print("=" * 60)
    
    validator = SmartSymbolValidator()
    
    # 測試案例
    test_cases = [
        # (symbol, context, entity_type, expected_valid, description)
        ("AAPL", "I'm buying AAPL stock today", "stock", True, "真實股票 + 股票上下文"),
        ("TSLA", "TSLA to the moon! 🚀", "stock", True, "真實股票 + meme 上下文"),
        ("BTC", "Bitcoin (BTC) is pumping", "crypto", True, "真實加密貨幣 + 加密貨幣上下文"),
        ("ETH", "Ethereum ETH price is rising", "crypto", True, "真實加密貨幣 + 價格上下文"),
        ("PRICE", "The price is going up", "stock", False, "錯誤識別的普通單詞"),
        ("VALUE", "This stock has great value", "stock", False, "錯誤識別的普通單詞"),
        ("MONEY", "I need more money", "stock", False, "錯誤識別的普通單詞"),
        ("STOCK", "The stock market is volatile", "stock", False, "錯誤識別的普通單詞"),
        ("COIN", "This coin is interesting", "crypto", False, "錯誤識別的普通單詞"),
        ("TRADE", "Let's trade this", "stock", False, "錯誤識別的普通單詞"),
        ("NVDA", "NVIDIA (NVDA) earnings beat expectations", "stock", True, "真實股票 + 財報上下文"),
        ("DOGE", "Dogecoin DOGE community is strong", "crypto", True, "真實加密貨幣 + 社區上下文"),
        ("FAKE", "FAKE is not a real ticker", "stock", False, "不存在的符號"),
        ("NOTREAL", "NOTREAL crypto doesn't exist", "crypto", False, "不存在的符號"),
        ("AMZN", "Amazon stock AMZN", "stock", True, "真實股票"),
        ("GOOGL", "Google parent company GOOGL", "stock", True, "真實股票"),
        ("META", "Meta platforms META", "stock", True, "真實股票"),
        ("SOL", "Solana SOL blockchain", "crypto", True, "真實加密貨幣"),
        ("ADA", "Cardano ADA staking", "crypto", True, "真實加密貨幣"),
        ("THE", "The market is down", "stock", False, "常見英文單詞"),
        ("AND", "Buy and hold", "stock", False, "常見英文單詞"),
        ("FOR", "This is for trading", "stock", False, "常見英文單詞"),
    ]
    
    print("測試案例:")
    print("-" * 60)
    
    correct_predictions = 0
    total_tests = len(test_cases)
    
    for symbol, context, entity_type, expected_valid, description in test_cases:
        is_valid, confidence, reason = validator.validate_symbol(symbol, context, entity_type)
        
        # 判斷預測是否正確
        prediction_correct = (is_valid == expected_valid)
        if prediction_correct:
            correct_predictions += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"{status} {symbol:8} | {entity_type:6} | 預期: {str(expected_valid):5} | 實際: {str(is_valid):5} | 信心度: {confidence:.2f} | {description}")
        if not prediction_correct:
            print(f"     原因: {reason}")
    
    accuracy = correct_predictions / total_tests * 100
    print("-" * 60)
    print(f"準確率: {correct_predictions}/{total_tests} = {accuracy:.1f}%")
    
    # 顯示緩存統計
    cache_stats = validator.get_cache_stats()
    print(f"緩存統計: {cache_stats}")
    
    return accuracy > 80  # 期望準確率超過80%


def test_entity_extractor_with_validation():
    """測試集成了智能驗證的實體提取器"""
    print("\n🔍 測試集成智能驗證的實體提取器...")
    print("=" * 60)
    
    extractor = EntityExtractor()
    
    # 測試文本（包含真實和虛假的符號）
    test_texts = [
        {
            "content": "I'm bullish on AAPL and TSLA. The price is going up and I think the value will increase. Also looking at BTC and ETH for crypto exposure.",
            "expected_valid": ["AAPL", "TSLA", "BTC", "ETH"],
            "expected_invalid": ["PRICE", "VALUE"]
        },
        {
            "content": "NVDA earnings were great! The stock market is volatile but I'm holding my shares. Also bought some DOGE for fun.",
            "expected_valid": ["NVDA", "DOGE"],
            "expected_invalid": ["STOCK", "MARKET"]
        },
        {
            "content": "Trading AMZN and GOOGL today. Money is tight but these are good investments. The trade went well.",
            "expected_valid": ["AMZN", "GOOGL"],
            "expected_invalid": ["MONEY", "TRADE"]
        }
    ]
    
    total_correct = 0
    total_tests = 0
    
    for i, test_case in enumerate(test_texts, 1):
        print(f"\n測試案例 {i}:")
        print(f"內容: {test_case['content']}")
        
        # 提取實體
        entities = extractor.extract_entities(test_case['content'], f"test_{i}", "post")
        
        extracted_symbols = [entity['symbol'] for entity in entities]
        print(f"提取到的符號: {extracted_symbols}")
        
        # 檢查預期的有效符號
        for symbol in test_case['expected_valid']:
            if symbol in extracted_symbols:
                print(f"  ✅ 正確識別: {symbol}")
                total_correct += 1
            else:
                print(f"  ❌ 遺漏: {symbol}")
            total_tests += 1
        
        # 檢查預期的無效符號（應該被過濾掉）
        for symbol in test_case['expected_invalid']:
            if symbol not in extracted_symbols:
                print(f"  ✅ 正確過濾: {symbol}")
                total_correct += 1
            else:
                print(f"  ❌ 錯誤識別: {symbol}")
            total_tests += 1
    
    accuracy = total_correct / total_tests * 100 if total_tests > 0 else 0
    print(f"\n整體準確率: {total_correct}/{total_tests} = {accuracy:.1f}%")
    
    return accuracy > 75  # 期望準確率超過75%


def test_performance():
    """測試性能"""
    print("\n⚡ 測試性能...")
    print("=" * 60)
    
    import time
    
    validator = SmartSymbolValidator()
    
    # 測試大量符號的驗證速度
    test_symbols = ["AAPL", "TSLA", "BTC", "ETH", "PRICE", "VALUE", "NVDA", "DOGE"] * 10
    
    start_time = time.time()
    
    for symbol in test_symbols:
        validator.validate_symbol(symbol, f"Trading {symbol} today", "stock")
    
    end_time = time.time()
    
    total_time = end_time - start_time
    avg_time = total_time / len(test_symbols)
    
    print(f"驗證 {len(test_symbols)} 個符號耗時: {total_time:.2f} 秒")
    print(f"平均每個符號: {avg_time*1000:.1f} 毫秒")
    
    # 測試緩存效果
    start_time = time.time()
    
    for symbol in test_symbols:  # 重複測試，應該使用緩存
        validator.validate_symbol(symbol, f"Trading {symbol} today", "stock")
    
    end_time = time.time()
    
    cached_time = end_time - start_time
    cached_avg = cached_time / len(test_symbols)
    
    print(f"緩存後驗證 {len(test_symbols)} 個符號耗時: {cached_time:.2f} 秒")
    print(f"緩存後平均每個符號: {cached_avg*1000:.1f} 毫秒")
    
    speedup = total_time / cached_time if cached_time > 0 else float('inf')
    print(f"緩存加速比: {speedup:.1f}x")
    
    return speedup > 2  # 期望緩存至少有2倍加速


def main():
    """主測試函數"""
    print("🧪 智能符號驗證系統測試")
    print("=" * 80)
    
    tests = [
        ("智能驗證器", test_smart_validator),
        ("實體提取器集成", test_entity_extractor_with_validation),
        ("性能測試", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 {test_name}測試...")
            if test_func():
                print(f"✅ {test_name}測試通過")
                passed += 1
            else:
                print(f"❌ {test_name}測試失敗")
        except Exception as e:
            print(f"❌ {test_name}測試出錯: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"🎯 測試結果: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！智能驗證系統工作正常。")
        print("\n💡 系統改進:")
        print("  ✅ 過濾掉 PRICE, VALUE, MONEY 等錯誤識別")
        print("  ✅ 保留真實的股票和加密貨幣符號")
        print("  ✅ 基於上下文的智能判斷")
        print("  ✅ 多層驗證機制")
        print("  ✅ 高效的緩存系統")
        return True
    else:
        print("⚠️  部分測試失敗，需要進一步調優。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
