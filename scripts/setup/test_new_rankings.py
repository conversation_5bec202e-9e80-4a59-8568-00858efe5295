#!/usr/bin/env python3
"""
測試新的排名功能
驗證修復重複問題和新增的排名類別
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator
from src.collectors.reddit_collector import RedditCollector
from src.analyzers.sentiment_analyzer import SentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor


def test_new_rankings():
    """測試新的排名功能"""
    print("🔍 測試新的排名功能...")
    print("=" * 60)
    
    try:
        # 首先收集一些測試數據
        print("📥 收集測試數據...")
        reddit_collector = RedditCollector()
        sentiment_analyzer = SentimentAnalyzer()
        entity_extractor = EntityExtractor()
        
        # 收集少量數據進行測試
        test_subreddits = ["stocks", "CryptoCurrency"]
        for subreddit in test_subreddits:
            posts = reddit_collector.collect_subreddit_posts(subreddit, limit=3)
            print(f"  - 從 r/{subreddit} 收集了 {len(posts)} 個帖子")
            
            # 分析前幾個帖子
            for post in posts[:2]:
                # 情緒分析
                sentiment_analyzer.analyze_sentiment(
                    f"{post['title']} {post['content']}", 
                    post['id'], 
                    'post'
                )
                
                # 實體提取
                entity_extractor.extract_entities(
                    f"{post['title']} {post['content']}", 
                    post['id'], 
                    'post'
                )
        
        # 計算趨勢
        trend_analyzer = TrendAnalyzer()
        trends = trend_analyzer.calculate_all_trends()
        print(f"✅ 趨勢計算完成: {trends}")
        
        # 測試各種排名
        print("\n📊 測試各種排名...")
        
        # 1. 傳統熱門排名
        print("\n1. 📈 熱門股票 (按趨勢分數):")
        top_stocks = trend_analyzer.get_top_trending("stock", "1h", 5)
        for i, stock in enumerate(top_stocks, 1):
            print(f"   {i}. {stock['symbol']}: 提及 {stock['mention_count']}, 趨勢分數 {stock['trend_score']:.2f}")
        
        print("\n2. 💰 熱門加密貨幣 (按趨勢分數):")
        top_cryptos = trend_analyzer.get_top_trending("crypto", "1h", 5)
        for i, crypto in enumerate(top_cryptos, 1):
            print(f"   {i}. {crypto['symbol']}: 提及 {crypto['mention_count']}, 趨勢分數 {crypto['trend_score']:.2f}")
        
        # 2. 按提及次數排名
        print("\n3. 📊 按提及次數排名:")
        mention_stocks = trend_analyzer.get_trending_by_mentions("stock", "1h", 3)
        for i, stock in enumerate(mention_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: {stock['mention_count']} 次提及")
        
        mention_cryptos = trend_analyzer.get_trending_by_mentions("crypto", "1h", 3)
        for i, crypto in enumerate(mention_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: {crypto['mention_count']} 次提及")
        
        # 3. 突然爆紅
        print("\n4. 🚀 突然爆紅 (動量排行):")
        momentum_stocks = trend_analyzer.get_trending_by_momentum("stock", "1h", 3)
        for i, stock in enumerate(momentum_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 動量比 {stock['momentum_ratio']:.1f}x")
        
        momentum_cryptos = trend_analyzer.get_trending_by_momentum("crypto", "1h", 3)
        for i, crypto in enumerate(momentum_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 動量比 {crypto['momentum_ratio']:.1f}x")
        
        # 4. 小眾熱門
        print("\n5. 💎 小眾熱門:")
        niche_stocks = trend_analyzer.get_niche_favorites("stock", "1h", 3)
        for i, stock in enumerate(niche_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 提及 {stock['mention_count']}, 情緒 {stock['avg_sentiment_score']:.2f}")
        
        niche_cryptos = trend_analyzer.get_niche_favorites("crypto", "1h", 3)
        for i, crypto in enumerate(niche_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 提及 {crypto['mention_count']}, 情緒 {crypto['avg_sentiment_score']:.2f}")
        
        # 5. 爭議項目
        print("\n6. ⚡ 爭議項目:")
        controversial_stocks = trend_analyzer.get_controversial("stock", "1h", 3)
        for i, stock in enumerate(controversial_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 正面 {stock['positive_sentiment_count']}, 負面 {stock['negative_sentiment_count']}")
        
        controversial_cryptos = trend_analyzer.get_controversial("crypto", "1h", 3)
        for i, crypto in enumerate(controversial_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 正面 {crypto['positive_sentiment_count']}, 負面 {crypto['negative_sentiment_count']}")
        
        # 6. 新興趨勢
        print("\n7. 🌱 新興趨勢:")
        emerging_stocks = trend_analyzer.get_emerging_trends("stock", "1h", 3)
        for i, stock in enumerate(emerging_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 新出現，提及 {stock['mention_count']}")
        
        emerging_cryptos = trend_analyzer.get_emerging_trends("crypto", "1h", 3)
        for i, crypto in enumerate(emerging_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 新出現，提及 {crypto['mention_count']}")
        
        # 7. Meme 潛力
        print("\n8. 🎭 Meme 潛力:")
        meme_stocks = trend_analyzer.get_meme_potential("stock", "1h", 3)
        for i, stock in enumerate(meme_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: Meme 分數 {stock['meme_score']:.2f}")
        
        meme_cryptos = trend_analyzer.get_meme_potential("crypto", "1h", 3)
        for i, crypto in enumerate(meme_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: Meme 分數 {crypto['meme_score']:.2f}")
        
        # 8. 小市值寶石
        print("\n9. 💍 小市值寶石:")
        gem_stocks = trend_analyzer.get_small_cap_gems("stock", "1h", 3)
        for i, stock in enumerate(gem_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 寶石分數 {stock['gem_score']:.2f}")
        
        gem_cryptos = trend_analyzer.get_small_cap_gems("crypto", "1h", 3)
        for i, crypto in enumerate(gem_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 寶石分數 {crypto['gem_score']:.2f}")
        
        # 9. 情緒領導者
        print("\n10. 😊 最正面情緒:")
        positive_stocks = trend_analyzer.get_sentiment_leaders("stock", "1h", 3, "positive")
        for i, stock in enumerate(positive_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 情緒 {stock['avg_sentiment_score']:.2f}")
        
        positive_cryptos = trend_analyzer.get_sentiment_leaders("crypto", "1h", 3, "positive")
        for i, crypto in enumerate(positive_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 情緒 {crypto['avg_sentiment_score']:.2f}")
        
        print("\n11. 😞 最負面情緒:")
        negative_stocks = trend_analyzer.get_sentiment_leaders("stock", "1h", 3, "negative")
        for i, stock in enumerate(negative_stocks, 1):
            print(f"   股票 {i}. {stock['symbol']}: 情緒 {stock['avg_sentiment_score']:.2f}")
        
        negative_cryptos = trend_analyzer.get_sentiment_leaders("crypto", "1h", 3, "negative")
        for i, crypto in enumerate(negative_cryptos, 1):
            print(f"   加密貨幣 {i}. {crypto['symbol']}: 情緒 {crypto['avg_sentiment_score']:.2f}")
        
        # 測試報告生成
        print("\n📊 測試新報告生成...")
        report_generator = ReportGenerator()
        report_path = report_generator.generate_hourly_report()
        
        print(f"✅ 新報告生成完成: {report_path}")
        
        print("\n" + "=" * 60)
        print("🎉 新排名功能測試完成！")
        print("=" * 60)
        
        print("\n📋 新功能總結:")
        print("  ✅ 修復重複結果問題")
        print("  ✅ 按趨勢分數排名")
        print("  ✅ 按提及次數排名")
        print("  ✅ 突然爆紅檢測")
        print("  ✅ 小眾熱門發現")
        print("  ✅ 爭議項目識別")
        print("  ✅ 新興趨勢檢測")
        print("  ✅ Meme 潛力評估")
        print("  ✅ 小市值寶石發現")
        print("  ✅ 情緒領導者排名")
        
        print(f"\n🌐 查看新報告: file://{os.path.abspath(report_path)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主測試函數"""
    print("🧪 Reddit 股票加密貨幣監控系統 - 新排名功能測試")
    print("=" * 80)
    
    success = test_new_rankings()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 新排名功能測試成功！")
        print("\n💡 新功能亮點:")
        print("  🚀 突然爆紅 - 發現動量激增的項目")
        print("  💎 小眾熱門 - 發現高情緒低提及的寶藏")
        print("  🎭 Meme 潛力 - 識別具有 meme 特徵的項目")
        print("  💍 小市值寶石 - 發現被低估的潛力項目")
        print("  ⚡ 爭議項目 - 識別情緒分化嚴重的項目")
        print("  🌱 新興趨勢 - 發現剛開始被討論的項目")
        return True
    else:
        print("❌ 新排名功能測試失敗，請檢查系統配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
