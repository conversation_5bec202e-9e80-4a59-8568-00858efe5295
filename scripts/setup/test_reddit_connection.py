#!/usr/bin/env python3
"""
測試 Reddit API 連接
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import Config
from src.collectors.reddit_collector import RedditCollector
from src.storage.database import DatabaseManager


def test_config():
    """測試配置"""
    print("🔧 測試配置...")
    if Config.validate():
        print("✅ 配置驗證通過")
        return True
    else:
        print("❌ 配置驗證失敗")
        return False


def test_database():
    """測試數據庫"""
    print("\n📊 測試數據庫...")
    try:
        db = DatabaseManager()
        print("✅ 數據庫連接成功")
        
        # 設置默認 subreddits
        db.setup_default_subreddits()
        
        # 獲取 subreddits 統計
        stock_subreddits = db.get_subreddits_by_category("stock")
        crypto_subreddits = db.get_subreddits_by_category("crypto")
        
        print(f"📈 股票 subreddits: {len(stock_subreddits)} 個")
        print(f"💰 加密貨幣 subreddits: {len(crypto_subreddits)} 個")
        
        return True
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        return False


def test_reddit_api():
    """測試 Reddit API"""
    print("\n🔗 測試 Reddit API...")
    try:
        collector = RedditCollector()
        
        if collector.test_connection():
            print("✅ Reddit API 連接成功")
            
            # 測試獲取一個 subreddit 的信息
            print("\n📋 測試獲取 subreddit 信息...")
            info = collector.get_subreddit_info("stocks")
            if info:
                print(f"  - 名稱: {info['name']}")
                print(f"  - 訂閱者: {info['subscribers']:,}")
                print(f"  - 活躍用戶: {info.get('active_users', 'N/A')}")
            
            # 測試收集少量帖子
            print("\n📝 測試收集帖子...")
            posts = collector.collect_subreddit_posts("stocks", limit=3)
            print(f"  - 收集到 {len(posts)} 個帖子")
            
            if posts:
                print(f"  - 示例帖子: {posts[0]['title'][:50]}...")
            
            return True
        else:
            print("❌ Reddit API 連接失敗")
            return False
            
    except Exception as e:
        print(f"❌ Reddit API 測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🚀 Reddit 股票加密貨幣監控系統 - 連接測試")
    print("=" * 50)
    
    # 測試配置
    if not test_config():
        return False
    
    # 測試數據庫
    if not test_database():
        return False
    
    # 測試 Reddit API
    if not test_reddit_api():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有測試通過！系統準備就緒。")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
