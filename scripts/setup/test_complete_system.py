#!/usr/bin/env python3
"""
完整系統測試
測試整個 Reddit 股票加密貨幣情緒監控系統
"""

import sys
import os
import time

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import Config
from src.collectors.reddit_collector import RedditCollector
from src.analyzers.sentiment_analyzer import SentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor
from src.analyzers.trend_analyzer import TrendAnalyzer
from src.reporters.report_generator import ReportGenerator
from src.storage.database import DatabaseManager


def test_complete_workflow():
    """測試完整的工作流程"""
    print("🚀 開始完整系統測試...")
    print("=" * 60)
    
    try:
        # 1. 驗證配置
        print("🔧 步驟 1: 驗證配置...")
        config = Config()
        if not config.validate():
            print("❌ 配置驗證失敗")
            return False
        print("✅ 配置驗證通過")
        
        # 2. 初始化數據庫
        print("\n📊 步驟 2: 初始化數據庫...")
        db = DatabaseManager()
        db.setup_default_subreddits()
        print("✅ 數據庫初始化完成")
        
        # 3. 測試 Reddit 數據收集
        print("\n📥 步驟 3: 測試 Reddit 數據收集...")
        reddit_collector = RedditCollector()
        
        # 收集少量測試數據
        test_subreddits = ["stocks", "CryptoCurrency"]
        total_posts = 0
        
        for subreddit in test_subreddits:
            posts = reddit_collector.collect_subreddit_posts(subreddit, limit=5)
            total_posts += len(posts)
            print(f"  - 從 r/{subreddit} 收集了 {len(posts)} 個帖子")
            
            # 為前3個帖子收集評論
            for post in posts[:3]:
                comments = reddit_collector.collect_post_comments(post['id'], limit=5)
                print(f"    * 帖子 {post['id']} 收集了 {len(comments)} 個評論")
        
        print(f"✅ 數據收集完成: 總共 {total_posts} 個帖子")
        
        # 4. 測試情緒分析
        print("\n🎭 步驟 4: 測試情緒分析...")
        sentiment_analyzer = SentimentAnalyzer()
        
        posts_analyzed = sentiment_analyzer.analyze_recent_posts(hours=1)
        comments_analyzed = sentiment_analyzer.analyze_recent_comments(hours=1)
        
        print(f"✅ 情緒分析完成: {posts_analyzed} 個帖子, {comments_analyzed} 個評論")
        
        # 5. 測試實體提取
        print("\n🔍 步驟 5: 測試實體提取...")
        entity_extractor = EntityExtractor()
        
        posts_entities = entity_extractor.extract_from_recent_posts(hours=1)
        comments_entities = entity_extractor.extract_from_recent_comments(hours=1)
        
        print(f"✅ 實體提取完成: 帖子 {posts_entities} 個實體, 評論 {comments_entities} 個實體")
        
        # 6. 測試趨勢分析
        print("\n📈 步驟 6: 測試趨勢分析...")
        trend_analyzer = TrendAnalyzer()
        
        trends = trend_analyzer.calculate_all_trends()
        print(f"✅ 趨勢計算完成: {trends}")
        
        # 獲取熱門趨勢
        top_stocks = trend_analyzer.get_top_trending("stock", "1h", 5)
        top_cryptos = trend_analyzer.get_top_trending("crypto", "1h", 5)
        
        print(f"  - 熱門股票: {len(top_stocks)} 個")
        for stock in top_stocks[:3]:
            print(f"    * {stock['symbol']}: {stock['mention_count']} 次提及")
        
        print(f"  - 熱門加密貨幣: {len(top_cryptos)} 個")
        for crypto in top_cryptos[:3]:
            print(f"    * {crypto['symbol']}: {crypto['mention_count']} 次提及")
        
        # 7. 測試報告生成
        print("\n📊 步驟 7: 測試報告生成...")
        report_generator = ReportGenerator()
        
        # 生成每小時報告
        hourly_report = report_generator.generate_hourly_report()
        print(f"✅ 每小時報告生成: {hourly_report}")
        
        # 生成每日報告
        daily_report = report_generator.generate_daily_report()
        print(f"✅ 每日報告生成: {daily_report}")
        
        # 8. 顯示統計信息
        print("\n📊 步驟 8: 系統統計...")
        
        # 獲取市場概覽
        market_overview = trend_analyzer.get_market_overview("1h")
        
        print("市場概覽:")
        print(f"  - 股票平均情緒: {market_overview['market_sentiment']['stocks']['avg_sentiment']:.2f}")
        print(f"  - 加密貨幣平均情緒: {market_overview['market_sentiment']['crypto']['avg_sentiment']:.2f}")
        print(f"  - 收集帖子: {market_overview['activity_stats']['posts_collected']}")
        print(f"  - 收集評論: {market_overview['activity_stats']['comments_collected']}")
        print(f"  - 分析情緒: {market_overview['activity_stats']['sentiments_analyzed']}")
        print(f"  - 提取實體: {market_overview['activity_stats']['entities_extracted']}")
        
        # 9. 檢查報告文件
        print("\n📁 步驟 9: 檢查生成的報告文件...")
        
        if os.path.exists(hourly_report):
            file_size = os.path.getsize(hourly_report)
            print(f"✅ 每小時報告文件存在: {file_size} 字節")
        else:
            print("❌ 每小時報告文件不存在")
        
        if os.path.exists(daily_report):
            file_size = os.path.getsize(daily_report)
            print(f"✅ 每日報告文件存在: {file_size} 字節")
        else:
            print("❌ 每日報告文件不存在")
        
        print("\n" + "=" * 60)
        print("🎉 完整系統測試成功！")
        print("=" * 60)
        
        print("\n📋 測試總結:")
        print(f"  ✅ 配置驗證: 通過")
        print(f"  ✅ 數據庫初始化: 通過")
        print(f"  ✅ Reddit 數據收集: {total_posts} 個帖子")
        print(f"  ✅ 情緒分析: {posts_analyzed + comments_analyzed} 個項目")
        print(f"  ✅ 實體提取: {posts_entities + comments_entities} 個實體")
        print(f"  ✅ 趨勢分析: {sum(trends.values())} 個趨勢")
        print(f"  ✅ 報告生成: 2 個報告文件")
        
        print(f"\n🌐 查看報告:")
        print(f"  - 每小時報告: file://{os.path.abspath(hourly_report)}")
        print(f"  - 每日報告: file://{os.path.abspath(daily_report)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 系統測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_scheduler_setup():
    """測試調度器設置"""
    print("\n🕐 測試調度器設置...")
    
    try:
        from src.schedulers.task_scheduler import TaskScheduler
        
        scheduler = TaskScheduler()
        
        # 獲取任務狀態
        job_status = scheduler.get_job_status()
        
        print(f"✅ 調度器設置成功，共 {len(job_status)} 個任務:")
        for job in job_status:
            print(f"  - {job['name']} ({job['id']})")
            print(f"    下次運行: {job['next_run']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 調度器測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🧪 Reddit 股票加密貨幣情緒監控系統 - 完整測試")
    print("=" * 80)
    
    # 運行完整工作流程測試
    workflow_success = test_complete_workflow()
    
    # 測試調度器設置
    scheduler_success = test_scheduler_setup()
    
    print("\n" + "=" * 80)
    if workflow_success and scheduler_success:
        print("🎉 所有測試通過！系統準備就緒，可以開始 24/7 監控。")
        print("\n🚀 啟動系統:")
        print("  python main.py                # 啟動完整監控系統")
        print("  python main.py --test         # 運行測試模式")
        print("  python main.py --report       # 只生成報告")
        return True
    else:
        print("❌ 部分測試失敗，請檢查配置和網絡連接。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
