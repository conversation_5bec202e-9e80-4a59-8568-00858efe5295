#!/usr/bin/env python3
"""
啟動 Reddit 股票加密貨幣情緒監控系統
演示 24/7 運行模式
"""

import sys
import os
import time
from datetime import datetime

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.schedulers.task_scheduler import TaskScheduler
from src.utils.config import Config


def main():
    """主函數 - 演示系統運行"""
    print("🚀 Reddit 股票加密貨幣情緒監控系統")
    print("=" * 60)
    print(f"啟動時間: {datetime.now()}")
    print("=" * 60)
    
    # 驗證配置
    config = Config()
    if not config.validate():
        print("❌ 配置驗證失敗，請檢查 .env 文件")
        return
    
    print("✅ 配置驗證通過")
    print("\n📋 系統將執行以下任務:")
    print("  🔄 每小時收集 Reddit 數據")
    print("  🎭 每小時分析情緒")
    print("  🔍 每小時提取實體")
    print("  📈 每小時計算趨勢")
    print("  📊 每小時生成報告")
    print("  🧹 每日清理舊數據")
    print("  💓 每 5 分鐘系統心跳檢查")
    
    print(f"\n📁 報告將保存到: {config.REPORTS_DIR}/")
    print("🌐 查看最新報告: reports/latest_hourly.html")
    
    print("\n" + "=" * 60)
    print("⚠️  注意: 這是演示模式，實際 24/7 運行請使用:")
    print("   python main.py")
    print("=" * 60)
    
    # 詢問用戶是否繼續
    try:
        response = input("\n是否開始演示運行？(y/N): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("👋 演示取消")
            return
    except KeyboardInterrupt:
        print("\n👋 演示取消")
        return
    
    print("\n🎬 開始演示運行...")
    print("按 Ctrl+C 停止演示")
    
    try:
        # 創建調度器
        scheduler = TaskScheduler()
        
        # 立即執行一次完整流程作為演示
        print("\n🔄 執行演示數據收集...")
        scheduler.collect_data_job()
        
        print("\n🎭 執行演示情緒分析...")
        scheduler.analyze_sentiment_job()
        
        print("\n🔍 執行演示實體提取...")
        scheduler.extract_entities_job()
        
        print("\n📈 執行演示趨勢計算...")
        scheduler.calculate_trends_job()
        
        print("\n📊 執行演示報告生成...")
        scheduler.generate_report_job()
        
        print("\n✅ 演示流程完成！")
        print(f"📁 查看生成的報告: {config.REPORTS_DIR}/")
        
        # 詢問是否啟動完整調度器
        try:
            response = input("\n是否啟動完整的調度器？(y/N): ").strip().lower()
            if response in ['y', 'yes', '是']:
                print("\n🚀 啟動完整調度器...")
                print("系統將按計劃自動運行所有任務")
                print("按 Ctrl+C 停止系統")
                
                # 啟動調度器
                scheduler.start()
            else:
                print("👋 演示結束")
        except KeyboardInterrupt:
            print("\n👋 演示結束")
            
    except KeyboardInterrupt:
        print("\n⏹️ 收到中斷信號，正在停止...")
    except Exception as e:
        print(f"\n❌ 演示運行失敗: {e}")
    finally:
        print("👋 感謝使用 Reddit 股票加密貨幣情緒監控系統！")


if __name__ == "__main__":
    main()
