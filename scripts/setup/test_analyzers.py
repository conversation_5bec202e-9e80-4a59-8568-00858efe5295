#!/usr/bin/env python3
"""
測試分析器模組
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analyzers.sentiment_analyzer import SentimentAnalyzer
from src.analyzers.entity_extractor import EntityExtractor
from src.analyzers.trend_analyzer import TrendAnalyzer


def test_sentiment_analyzer():
    """測試情緒分析器"""
    print("🎭 測試情緒分析器...")
    
    try:
        analyzer = SentimentAnalyzer()
        
        # 測試 API 連接
        if analyzer.test_api_connection():
            print("✅ OpenRouter API 連接成功")
            
            # 測試情緒分析
            test_content = "I'm really bullish on TSLA! The stock is going to the moon! 🚀"
            result = analyzer.analyze_sentiment(test_content, "test_001", "post")
            
            if result:
                print(f"  - 測試內容: {test_content}")
                print(f"  - 情緒分數: {result['sentiment_score']:.2f}")
                print(f"  - 情緒標籤: {result['sentiment_label']}")
                print(f"  - 信心度: {result['confidence']:.2f}")
                return True
            else:
                print("❌ 情緒分析測試失敗")
                return False
        else:
            print("❌ OpenRouter API 連接失敗")
            return False
            
    except Exception as e:
        print(f"❌ 情緒分析器測試失敗: {e}")
        return False


def test_entity_extractor():
    """測試實體提取器"""
    print("\n🔍 測試實體提取器...")
    
    try:
        extractor = EntityExtractor()
        
        # 測試實體提取
        test_content = "I'm buying more AAPL and BTC today. Also looking at TSLA and ETH for long term."
        entities = extractor.extract_entities(test_content, "test_002", "post")
        
        if entities:
            print(f"  - 測試內容: {test_content}")
            print(f"  - 提取到 {len(entities)} 個實體:")
            for entity in entities:
                print(f"    * {entity['type'].upper()}: {entity['symbol']} ({entity['confidence']:.2f})")
            return True
        else:
            print("❌ 未提取到任何實體")
            return False
            
    except Exception as e:
        print(f"❌ 實體提取器測試失敗: {e}")
        return False


def test_trend_analyzer():
    """測試趨勢分析器"""
    print("\n📈 測試趨勢分析器...")
    
    try:
        analyzer = TrendAnalyzer()
        
        # 計算趨勢
        trends = analyzer.calculate_all_trends()
        print(f"  - 趨勢計算結果: {trends}")
        
        # 獲取熱門趨勢
        top_stocks = analyzer.get_top_trending("stock", "24h", 5)
        top_cryptos = analyzer.get_top_trending("crypto", "24h", 5)
        
        print(f"  - 熱門股票 ({len(top_stocks)} 個):")
        for trend in top_stocks[:3]:
            print(f"    * {trend['symbol']}: {trend['mention_count']} 次提及, 趨勢分數: {trend['trend_score']:.2f}")
        
        print(f"  - 熱門加密貨幣 ({len(top_cryptos)} 個):")
        for trend in top_cryptos[:3]:
            print(f"    * {trend['symbol']}: {trend['mention_count']} 次提及, 趨勢分數: {trend['trend_score']:.2f}")
        
        # 獲取市場概覽
        overview = analyzer.get_market_overview("24h")
        print(f"  - 市場概覽生成成功")
        print(f"    * 股票平均情緒: {overview['market_sentiment']['stocks']['avg_sentiment']:.2f}")
        print(f"    * 加密貨幣平均情緒: {overview['market_sentiment']['crypto']['avg_sentiment']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 趨勢分析器測試失敗: {e}")
        return False


def test_integration():
    """測試集成流程"""
    print("\n🔄 測試集成流程...")
    
    try:
        # 創建分析器實例
        sentiment_analyzer = SentimentAnalyzer()
        entity_extractor = EntityExtractor()
        trend_analyzer = TrendAnalyzer()
        
        # 模擬一個完整的分析流程
        test_posts = [
            {
                'id': 'test_post_1',
                'content': 'AAPL is looking great! Buying more shares. Very bullish on Apple.',
                'type': 'post'
            },
            {
                'id': 'test_post_2', 
                'content': 'BTC to the moon! 🚀 Cryptocurrency is the future. Also buying ETH.',
                'type': 'post'
            },
            {
                'id': 'test_post_3',
                'content': 'TSLA earnings were disappointing. Might sell my position.',
                'type': 'post'
            }
        ]
        
        print("  - 處理測試帖子...")
        
        for post in test_posts:
            # 提取實體
            entities = entity_extractor.extract_entities(
                post['content'], post['id'], post['type']
            )
            
            # 分析情緒
            sentiment = sentiment_analyzer.analyze_sentiment(
                post['content'], post['id'], post['type']
            )
            
            print(f"    * 帖子 {post['id']}: {len(entities)} 個實體, 情緒: {sentiment['sentiment_label'] if sentiment else 'N/A'}")
        
        # 計算趨勢
        trends = trend_analyzer.calculate_all_trends()
        print(f"  - 趨勢重新計算: {trends}")
        
        print("✅ 集成測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🧪 Reddit 股票加密貨幣監控系統 - 分析器測試")
    print("=" * 60)
    
    tests = [
        ("情緒分析器", test_sentiment_analyzer),
        ("實體提取器", test_entity_extractor),
        ("趨勢分析器", test_trend_analyzer),
        ("集成流程", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試出錯: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 測試結果: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 所有分析器測試通過！")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查配置和網絡連接")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
